{"skeleton": {"hash": "MLg3NSc5UYNAgOJ6ShXJE0hxokQ", "spine": "3.7.94", "width": 174.01, "height": 128.74, "images": "./Image/", "audio": "D:/2020/Tulinh/Anim Final RW/Symbol_Fish/TL_Fish/Export"}, "bones": [{"name": "root"}, {"name": "All", "parent": "root", "x": 0.52, "y": -0.86, "scaleX": 1.1, "scaleY": 1.1}, {"name": "AllFish", "parent": "All", "x": -2.18, "y": 1.07}, {"name": "<PERSON><PERSON><PERSON>", "parent": "AllFish"}, {"name": "Fish", "parent": "AllFish", "y": -12.63, "color": "00bbffff"}, {"name": "<PERSON>", "parent": "AllFish"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "AllFish"}, {"name": "Fish2", "parent": "Fish", "length": 13.65, "rotation": -67.38, "x": 0.79, "y": 14.3, "color": "00bbffff"}, {"name": "Fish3", "parent": "Fish2", "length": 13.5, "rotation": -14.53, "x": 13.65, "color": "00bbffff"}, {"name": "Fish4", "parent": "Fish3", "length": 13.55, "rotation": -9.65, "x": 13.6, "y": -0.1, "color": "00bbffff"}, {"name": "Fish5", "parent": "Fish4", "length": 13.58, "rotation": -16.01, "x": 13.48, "y": 0.06, "color": "00bbffff"}, {"name": "Fish6", "parent": "Fish5", "length": 12.31, "rotation": -12.9, "x": 14.21, "y": -0.13, "color": "00bbffff"}, {"name": "Fish7", "parent": "Fish6", "rotation": -1.5, "x": 12.06, "y": -0.07, "color": "00bbffff"}, {"name": "DauCa", "parent": "Fish", "rotation": 2.27, "x": -1.12, "y": 20.01, "color": "00bbffff"}, {"name": "Rau2", "parent": "DauCa", "x": -12.57, "y": 21.81, "color": "00bbffff"}, {"name": "Rau1", "parent": "DauCa", "x": -5.97, "y": 24.69, "color": "00bbffff"}, {"name": "VayCa1", "parent": "Fish2", "x": 4.53, "y": -9.96, "color": "00bbffff"}, {"name": "VayCa2", "parent": "Fish2", "rotation": -36.15, "x": 8.16, "y": 17, "color": "00bbffff"}, {"name": "Fish9", "parent": "Fish7", "length": 11.28, "rotation": -43.62, "x": 0.06, "y": -0.22, "color": "00bbffff"}, {"name": "Fish10", "parent": "Fish9", "length": 10.41, "rotation": -1.43, "x": 11.28, "color": "00bbffff"}, {"name": "Fish11", "parent": "Fish10", "length": 7.68, "rotation": -18.82, "x": 10.41, "color": "00bbffff"}, {"name": "Fish12", "parent": "Fish7", "length": 11.25, "rotation": 8.24, "x": 0.14, "y": -0.35, "color": "00bbffff"}, {"name": "Fish13", "parent": "Fish12", "length": 11.31, "rotation": -4.26, "x": 11.25, "color": "00bbffff"}, {"name": "Fish14", "parent": "Fish13", "length": 8.14, "rotation": 15.81, "x": 11.31, "color": "00bbffff"}, {"name": "Head3D", "parent": "Fish", "x": -0.66, "y": 151.46, "color": "38ff03ff"}, {"name": "HeadTrai", "parent": "Fish", "y": 99.42, "color": "00bbffff"}, {"name": "HeadPhai", "parent": "Fish", "y": 150.34, "color": "00bbffff"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "All", "x": -19.49, "y": -54.58}, {"name": "EffNuoc2", "parent": "All", "x": -22.32, "y": -14.42, "scaleX": -1}, {"name": "VayCa3", "parent": "VayCa1", "length": 9.12, "rotation": -111.29, "x": -1.5, "y": -5.43, "color": "00bbffff"}, {"name": "VayCa4", "parent": "VayCa3", "length": 10.44, "rotation": -7.16, "x": 9.12, "color": "00bbffff"}, {"name": "EffNuoc3", "parent": "All", "x": 22.78, "y": -28.16}, {"name": "Nuoc2", "parent": "<PERSON><PERSON><PERSON>", "rotation": 1.28, "x": -31.55, "y": -19.07}, {"name": "Nuoc3", "parent": "<PERSON><PERSON><PERSON>", "x": 36.48, "y": -15.55}, {"name": "Star", "parent": "All", "x": -27.54, "y": 13.11, "scaleX": 1.08, "scaleY": 1.08}, {"name": "Star2", "parent": "All", "x": 26.29, "y": 40.14, "scaleX": 0.514, "scaleY": 0.514}, {"name": "Star3", "parent": "All", "x": 22.42, "y": -9.26, "scaleX": 0.811, "scaleY": 0.811}, {"name": "Star4", "parent": "All", "x": -51.84, "y": 6.25, "scaleX": 0.618, "scaleY": 0.618}, {"name": "Star5", "parent": "All", "x": -15.63, "y": -38.17, "scaleX": 0.956, "scaleY": 0.956}, {"name": "Star6", "parent": "All", "x": 9.42, "y": -27.5, "scaleX": 0.505, "scaleY": 0.505}, {"name": "Star7", "parent": "All", "x": 37.13, "y": -38.76, "scaleX": 0.811, "scaleY": 0.811}, {"name": "Khung2", "parent": "AllFish", "x": 1.54, "scaleX": 1.25, "scaleY": 1.25}], "slots": [{"name": "BG", "bone": "Khung2", "attachment": "BG"}, {"name": "Mat<PERSON>uoc<PERSON>lip", "bone": "<PERSON><PERSON><PERSON>", "attachment": "Mat<PERSON>uoc<PERSON>lip"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "CaClip", "bone": "AllFish", "attachment": "Cal<PERSON>"}, {"name": "VayCa2", "bone": "VayCa2", "attachment": "VayCa2"}, {"name": "ThanCa", "bone": "Fish", "attachment": "ThanCa"}, {"name": "DuoiCa", "bone": "Fish7", "attachment": "DuoiCa"}, {"name": "Rau1", "bone": "Rau1", "attachment": "Rau1"}, {"name": "Rau2", "bone": "Rau2", "attachment": "Rau2"}, {"name": "VayCa", "bone": "VayCa1", "attachment": "VayCa"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "Water/Water 02"}, {"name": "EffNuoc2", "bone": "EffNuoc2", "attachment": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00000"}, {"name": "EffNuoc3", "bone": "EffNuoc3", "attachment": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00000"}, {"name": "LaSen", "bone": "<PERSON>", "attachment": "LaSen"}, {"name": "BupSen", "bone": "<PERSON>", "attachment": "BupSen"}, {"name": "HoaSen", "bone": "<PERSON>", "attachment": "HoaSen"}, {"name": "efect_light2 copy 7", "bone": "Star", "attachment": "efect_light2 copy 7"}, {"name": "efect_light2 copy 8", "bone": "Star2", "attachment": "efect_light2 copy 7"}, {"name": "efect_light2 copy 9", "bone": "Star3", "attachment": "efect_light2 copy 7"}, {"name": "efect_light2 copy 10", "bone": "Star4", "attachment": "efect_light2 copy 7"}, {"name": "efect_light2 copy 11", "bone": "Star5", "attachment": "efect_light2 copy 7"}, {"name": "efect_light2 copy 12", "bone": "Star6", "attachment": "efect_light2 copy 7"}, {"name": "efect_light2 copy 13", "bone": "Star7", "attachment": "efect_light2 copy 7"}], "transform": [{"name": "HeadPhai", "order": 0, "bones": ["HeadPhai"], "target": "Head3D", "y": 83.96, "rotateMix": 0, "translateMix": -0.25, "scaleMix": 0, "shearMix": 0}, {"name": "HeadTrai", "order": 1, "bones": ["HeadTrai"], "target": "Head3D", "y": 36.22, "rotateMix": 0, "translateMix": 0.25, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "order": 3, "bones": ["Rau1"], "target": "Head3D", "rotation": 2.27, "x": -8.02, "y": -11.98, "rotateMix": 0, "translateMix": -0.01, "scaleMix": 0, "shearMix": 0}, {"name": "RauTrai", "order": 2, "bones": ["Rau2"], "target": "Head3D", "rotation": 2.27, "x": -14.43, "y": -15.09, "rotateMix": 0, "translateMix": 0.01, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "order": 5, "bones": ["VayCa2"], "target": "Head3D", "rotation": -67.38, "x": 21.62, "y": -50.91, "rotateMix": 0, "translateMix": -0.01, "scaleMix": 0, "shearMix": 0}, {"name": "VayTrai", "order": 4, "bones": ["VayCa1"], "target": "Head3D", "rotation": -67.38, "x": 12.91, "y": -52.83, "rotateMix": 0, "translateMix": 0.01, "scaleMix": 0, "shearMix": 0}], "skins": {"default": {"BG": {"BG": {"x": -0.5, "y": -2.03, "width": 79, "height": 79}}, "BupSen": {"BupSen": {"x": -38.96, "y": -9.53, "width": 27, "height": 58}}, "CaClip": {"Calip": {"type": "clipping", "end": "VayCa", "vertexCount": 17, "vertices": [-109.32, 36.12, -48.92, 16.7, -51.7, 8.38, -50.35, -2.84, -44.95, -7.89, -43.52, -14.47, -38, -26.67, -33.49, -32.49, -28.08, -37.82, -22.01, -42.25, 26.41, -41.28, 35.15, -34.2, 40.07, -28.09, 44.39, -20.35, 115.56, -0.07, 115.49, 151.38, -106.62, 152.27], "color": "00c8ffff"}}, "DuoiCa": {"DuoiCa": {"type": "mesh", "uvs": [0.03965, 0.2704, 0.36242, 0.16206, 0.71002, 0.02663, 0.9267, 0.04017, 0.98539, 0.31103, 0.87479, 0.66089, 0.69196, 0.96785, 0.6029, 0.99999, 0.54184, 0.87028, 0.55784, 0.56176, 0.47496, 0.48923, 0.17419, 0.42267, 0.03618, 0.39213], "triangles": [6, 8, 5, 7, 8, 6, 4, 9, 10, 5, 9, 4, 8, 9, 5, 10, 2, 4, 11, 0, 1, 12, 0, 11, 10, 1, 2, 11, 1, 10, 4, 2, 3], "vertices": [1, 20, 8.22, -2.48, 1, 3, 18, 16.65, -4.54, 0.00044, 19, 5.48, -4.4, 0.95662, 20, -3.24, -5.76, 0.04295, 3, 18, 3.6, -4.19, 0.66124, 19, -7.57, -4.38, 0.0028, 21, -1.14, -5.27, 0.33595, 2, 12, -2.83, 1.41, 0.54906, 18, -3.21, -0.81, 0.45094, 3, 12, 5.04, 7.08, 0.2309, 21, 5.92, 6.65, 0.7621, 22, -5.81, 6.24, 0.00701, 4, 12, 17.81, 8.43, 5e-05, 21, 18.75, 6.15, 0.05043, 22, 7.03, 6.69, 0.78737, 23, -2.29, 7.61, 0.16215, 1, 23, 8.85, 1.93, 1, 1, 23, 10.17, -1.11, 1, 1, 23, 5.78, -3.54, 1, 4, 19, 4.22, 11.12, 0.20193, 21, 18.33, -5.46, 0.00439, 22, 7.47, -4.92, 0.77705, 23, -5.03, -3.69, 0.01663, 4, 19, 5.99, 7.69, 0.63545, 20, -6.66, 5.86, 0.00252, 21, 16.65, -8.93, 0.01586, 22, 6.05, -8.5, 0.34616, 3, 19, 14.95, 1.68, 0.00101, 20, 3.75, 3.06, 0.99867, 22, 7.39, -19.2, 0.00032, 1, 20, 9.35, 1.14, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 24, 0, 24, 20, 22, 22, 24], "width": 35, "height": 35}, "DuoiCa2": {"x": -9, "y": -11.42, "rotation": 121.96, "width": 68, "height": 52}}, "EffNuoc": {"Water/Water 02": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 03": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 04": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 05": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 06": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 07": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 08": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 09": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 10": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 11": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 12": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 13": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 14": {"y": 42.38, "width": 136, "height": 70}, "Water/Water 15": {"y": 42.38, "width": 136, "height": 70}}, "EffNuoc2": {"Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00000": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00001": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00002": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00003": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00004": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00005": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00006": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00007": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00008": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00009": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00010": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00011": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00012": {"width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00013": {"width": 59, "height": 53}}, "EffNuoc3": {"Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00000": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00001": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00002": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00003": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00004": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00005": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00006": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00007": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00008": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00009": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00010": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00011": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00012": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}, "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00013": {"x": 18.42, "y": 35.02, "width": 59, "height": 53}}, "HoaSen": {"HoaSen": {"path": "Layer 8", "x": 25.04, "y": -37.53, "width": 53, "height": 44}}, "Khugn": {"Khugn": {"x": 1.54, "y": -1.53, "width": 106, "height": 106}}, "LaSen": {"LaSen": {"x": -10.95, "y": -40.35, "rotation": 0.72, "width": 61, "height": 29}}, "MatNuoc": {"MatNuoc": {"type": "mesh", "uvs": [0.04013, 0.19802, 0.22931, 0.18016, 0.46366, 0.1623, 0.7206, 0.09532, 0.98884, 0, 1, 0.69811, 0.69237, 1, 0.41002, 1, 0.00908, 0.5597, 0.26418, 0.39158, 0.38023, 0.32674, 0.74967, 0.21889, 0.56838, 0.35852, 0.23492, 0.31256, 0.45125, 0.48315, 0.71438, 0.38585, 0.32829, 0.32645, 0.63829, 0.22419, 0.49904, 0.28367, 0.71541, 0.28961, 0.77645, 0.16333, 0.36031, 0.26327, 0.3544, 0.45237], "triangles": [21, 1, 2, 21, 2, 18, 13, 1, 21, 0, 1, 13, 16, 13, 21, 10, 21, 18, 16, 21, 10, 9, 13, 16, 22, 16, 10, 9, 16, 22, 14, 10, 18, 14, 18, 12, 22, 10, 14, 8, 0, 13, 8, 13, 9, 7, 22, 14, 8, 9, 22, 7, 8, 22, 7, 14, 6, 20, 3, 4, 11, 3, 20, 17, 2, 3, 11, 17, 3, 18, 2, 17, 19, 17, 11, 12, 18, 17, 12, 17, 19, 15, 12, 19, 11, 20, 4, 5, 11, 4, 15, 19, 11, 5, 15, 11, 14, 12, 15, 6, 14, 15, 5, 6, 15], "vertices": [1, 32, -29.61, 16.81, 1, 2, 33, -66.97, 11.78, 0.0013, 32, -3.88, 15.16, 0.9987, 2, 33, -35.14, 9.28, 0.41108, 32, 27.92, 12.66, 0.58892, 2, 33, 0.24, 10.56, 0.98723, 32, 63.26, 13.94, 0.01277, 1, 33, 37.45, 14.19, 1, 2, 33, 31.55, -45.57, 0.97348, 32, 94.54, -42.14, 0.02652, 2, 33, -13.16, -66.14, 0.74507, 32, 49.87, -62.69, 0.25493, 2, 33, -51.27, -61.33, 0.29326, 32, 11.8, -57.88, 0.70674, 1, 32, -37.64, -13.53, 1, 2, 33, -64.51, -6.87, 0.01154, 32, -1.42, -3.47, 0.98846, 2, 33, -48.14, -3.31, 0.17412, 32, 14.92, 0.08, 0.82588, 1, 33, 2.85, -0.48, 1, 2, 33, -23.09, -9.28, 0.654, 32, 39.96, -5.88, 0.346, 1, 32, -4.53, 3.77, 1, 2, 33, -40.22, -17.89, 0.34453, 32, 22.84, -14.49, 0.65547, 2, 33, -3.68, -14.12, 0.92095, 32, 59.34, -10.72, 0.07905, 2, 33, -59.59, -2.37, 0.11041, 32, 8.47, 0.96, 0.88959, 2, 33, -13.4, 1.11, 0.77491, 32, 54.73, 3.41, 0.22509, 2, 33, -34.25, -1.43, 0.48568, 32, 33.82, 1.33, 0.51432, 2, 33, -2.88, -5.86, 0.92753, 32, 65.08, -3.8, 0.07247, 2, 33, 7.04, 3.82, 0.99736, 32, 75.22, 5.66, 0.00264, 2, 33, -54.27, 2.52, 0.19164, 32, 13.89, 5.73, 0.80836, 2, 33, -56.78, -13.58, 0.19725, 32, 11.02, -10.3, 0.80275], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 10, 12, 16, 0], "width": 108, "height": 68}}, "MatNuocClip": {"MatNuocClip": {"type": "clipping", "end": "<PERSON><PERSON><PERSON><PERSON>", "vertexCount": 15, "vertices": [-45.76, -13.3, -44.4, -20.68, -40.79, -26.67, -36.79, -32.49, -32.14, -37.82, -26.07, -42.25, 26.41, -41.28, 37.41, -34.2, 42.12, -28.09, 47.26, -20.35, 49.07, -12.79, 50.04, -5.73, 48.89, 1.32, 66.49, 69.44, -66.55, 61.11], "color": "ce3a3aff"}}, "Rau1": {"Rau1": {"x": 17.61, "y": -0.57, "width": 41, "height": 15}}, "Rau2": {"Rau2": {"x": -12.79, "y": -10.19, "width": 35, "height": 30}}, "ThanCa": {"ThanCa": {"type": "mesh", "uvs": [0.32049, 0.03319, 0.42051, 0.05365, 0.55051, 0.09426, 0.66876, 0.14803, 0.75945, 0.19628, 0.85849, 0.2795, 0.98495, 0.31371, 0.9838, 0.45475, 0.95752, 0.60994, 0.90159, 0.69873, 0.81759, 0.79597, 0.71504, 0.86376, 0.46424, 0.96148, 0.16581, 1, 0.30819, 0.89633, 0.35126, 0.86222, 0.39868, 0.82468, 0.41151, 0.79421, 0.42865, 0.75351, 0.41053, 0.71159, 0.39001, 0.66414, 0.33027, 0.61234, 0.27822, 0.56721, 0.18273, 0.46212, 0.12549, 0.38087, 0.07248, 0.31753, 0.03905, 0.25174, 0.04255, 0.166, 0.02689, 0.12043, 0.03882, 0.05382, 0.09919, 0.02423, 0.2026, 0.00371, 0.56373, 0.91696, 0.28563, 0.03978, 0.69681, 0.27276, 0.80636, 0.38047, 0.83705, 0.51047, 0.80954, 0.65693, 0.70036, 0.77419, 0.57272, 0.8559, 0.4699, 0.90604, 0.0958, 0.15089, 0.16722, 0.07808, 0.11767, 0.10977, 0.61719, 0.23842, 0.54301, 0.27671, 0.48847, 0.31042, 0.41647, 0.34299, 0.33792, 0.36585, 0.24192, 0.36642, 0.14701, 0.35385, 0.20722, 0.04811, 0.08929, 0.18017, 0.08642, 0.23283, 0.11084, 0.28776, 0.16542, 0.32462, 0.24442, 0.33741, 0.3306, 0.33064, 0.40386, 0.3013, 0.46418, 0.27421, 0.54031, 0.25089, 0.14058, 0.14414, 0.12663, 0.18404, 0.15238, 0.21719, 0.21245, 0.22618, 0.27574, 0.21832, 0.31121, 0.18759, 0.30685, 0.15201, 0.26494, 0.12653, 0.19414, 0.12392, 0.20127, 0.14222, 0.16996, 0.15701, 0.16083, 0.17909, 0.21456, 0.20679, 0.25916, 0.20085, 0.27612, 0.15741, 0.24651, 0.14555, 0.1759, 0.20103, 0.27603, 0.18111, 0.21932, 0.1744, 0.72929, 0.54322, 0.66602, 0.37503, 0.66805, 0.69651, 0.56864, 0.80775, 0.44878, 0.88413, 0.43116, 0.44844, 0.52068, 0.5695, 0.5124, 0.68134, 0.47115, 0.77193, 0.41517, 0.8515, 0.32809, 0.10314, 0.41802, 0.14981, 0.48659, 0.19946, 0.23479, 0.08746, 0.17161, 0.11375], "triangles": [33, 31, 0, 51, 31, 33, 42, 30, 31, 51, 42, 31, 90, 93, 51, 42, 51, 93, 90, 0, 1, 33, 0, 90, 90, 51, 33, 43, 30, 42, 29, 30, 43, 69, 94, 42, 43, 42, 94, 28, 29, 43, 93, 69, 42, 68, 93, 90, 69, 93, 68, 76, 70, 69, 61, 43, 94, 61, 94, 69, 68, 76, 69, 91, 90, 1, 91, 1, 2, 41, 28, 43, 41, 43, 61, 67, 68, 90, 67, 90, 91, 71, 61, 69, 71, 69, 70, 75, 68, 67, 76, 68, 75, 27, 28, 41, 79, 70, 76, 79, 76, 75, 71, 70, 79, 72, 61, 71, 72, 71, 79, 52, 27, 41, 78, 79, 75, 62, 41, 61, 62, 61, 72, 52, 41, 62, 66, 67, 91, 78, 75, 67, 66, 78, 67, 92, 91, 2, 74, 79, 78, 77, 72, 79, 73, 77, 79, 73, 79, 74, 63, 62, 72, 63, 72, 77, 65, 74, 78, 65, 78, 66, 64, 77, 73, 63, 77, 64, 64, 73, 74, 64, 74, 65, 53, 27, 52, 53, 52, 62, 53, 62, 63, 44, 2, 3, 60, 92, 2, 44, 60, 2, 26, 27, 53, 54, 53, 63, 58, 66, 91, 57, 65, 66, 54, 25, 26, 54, 26, 53, 55, 63, 64, 54, 63, 55, 56, 64, 65, 16, 17, 89, 15, 16, 89, 84, 89, 88, 40, 84, 39, 32, 40, 39, 12, 84, 40, 12, 40, 32, 12, 32, 11, 15, 12, 14, 84, 15, 89, 12, 15, 84, 13, 14, 12, 18, 19, 87, 88, 18, 87, 17, 18, 88, 38, 37, 10, 83, 87, 82, 83, 82, 38, 88, 87, 83, 89, 17, 88, 39, 83, 38, 11, 38, 10, 39, 38, 11, 84, 88, 83, 84, 83, 39, 32, 39, 11, 37, 36, 8, 20, 21, 86, 87, 20, 86, 82, 80, 37, 87, 86, 82, 9, 37, 8, 19, 20, 87, 38, 82, 37, 10, 37, 9, 36, 35, 7, 80, 35, 36, 86, 81, 80, 22, 85, 86, 8, 36, 7, 21, 22, 86, 37, 80, 36, 82, 86, 80, 34, 3, 4, 44, 3, 34, 59, 91, 92, 59, 92, 60, 45, 60, 44, 59, 60, 45, 34, 4, 5, 58, 91, 59, 46, 59, 45, 58, 57, 66, 56, 65, 57, 55, 64, 56, 47, 58, 59, 47, 59, 46, 57, 58, 47, 50, 54, 55, 25, 54, 50, 48, 57, 47, 49, 55, 56, 49, 56, 57, 49, 57, 48, 50, 55, 49, 81, 44, 34, 45, 44, 81, 35, 34, 5, 81, 34, 35, 24, 25, 50, 85, 47, 46, 48, 47, 85, 81, 85, 46, 81, 46, 45, 6, 35, 5, 7, 35, 6, 23, 50, 49, 24, 50, 23, 23, 49, 48, 23, 48, 85, 80, 81, 35, 22, 23, 85, 86, 85, 81], "vertices": [3, 13, -5.78, 26.52, 0.9024, 25, -12.23, -40.37, 0.04, 26, -12.23, -93.29, 0.0576, 4, 7, -41.33, 9.19, 0.00143, 13, -0.26, 23.83, 0.84229, 25, -6.62, -42.83, 0.05, 26, -6.62, -95.76, 0.10628, 4, 7, -32.39, 14.02, 0.14918, 13, 7.38, 17.13, 0.69455, 25, 1.28, -49.23, 0.05, 26, 1.28, -102.16, 0.10628, 5, 7, -21.33, 17.67, 0.35688, 8, -38.29, 8.33, 0.01144, 13, 14.64, 8.03, 0.47541, 25, 8.9, -58.03, 0.05, 26, 8.9, -110.96, 0.10628, 5, 7, -11.41, 20.14, 0.49945, 8, -29.31, 13.21, 0.02022, 13, 20.41, -0.41, 0.22856, 25, 15, -66.24, 0.1, 26, 15, -119.17, 0.15177, 5, 7, 1.56, 21.65, 0.56839, 8, -17.14, 17.92, 0.09558, 13, 26.33, -12.04, 0.06524, 25, 21.38, -77.63, 0.12, 26, 21.38, -130.56, 0.15079, 6, 7, 8.32, 26.65, 0.60141, 8, -11.85, 24.46, 0.20025, 9, -29.2, 19.95, 0.00179, 13, 33.37, -16.64, 0.02315, 25, 28.59, -81.95, 0.1, 26, 28.59, -134.87, 0.0734, 4, 7, 22.32, 20.87, 0.35481, 8, 3.15, 22.38, 0.5408, 9, -14.06, 20.42, 0.10433, 13, 32.82, -31.78, 5e-05, 4, 7, 36.81, 13.27, 0.02384, 8, 19.09, 18.66, 0.36374, 9, 2.27, 19.42, 0.58668, 10, -16.11, 15.52, 0.02575, 4, 7, 44.23, 6.85, 0, 8, 27.88, 14.3, 0.08514, 9, 11.67, 16.59, 0.67835, 10, -6.3, 15.4, 0.2365, 3, 8, 37.34, 8.29, 0.00049, 9, 22, 12.26, 0.21523, 10, 4.83, 14.07, 0.78428, 3, 9, 29.27, 6.81, 0.01115, 10, 13.32, 10.85, 0.87465, 11, -3.32, 10.5, 0.11419, 1, 11, 12.52, 3.81, 1, 2, 11, 21.66, -3.28, 0.99997, 12, 9.67, -2.96, 3e-05, 2, 11, 10.97, -7.06, 0.46883, 12, -0.91, -7.01, 0.53117, 6, 9, 29.53, -13.2, 0.00022, 10, 19.08, -8.32, 0.11482, 11, 6.57, -6.9, 0.50079, 12, -5.31, -6.97, 0.33474, 25, -5.98, -162.42, 0.01, 26, -6.31, -170.01, 0.03943, 6, 9, 25.65, -10.69, 0.00047, 10, 14.66, -6.98, 0.24633, 11, 1.96, -6.58, 0.5287, 12, -9.93, -6.77, 0.09993, 25, -3.54, -135.94, 0.04, 26, -3.54, -188.87, 0.08458, 7, 8, 33.75, -13.76, 0.00086, 9, 22.16, -10.09, 0.05473, 10, 11.14, -7.36, 0.44049, 11, -1.38, -7.73, 0.31356, 12, -13.24, -8.01, 0.05773, 25, -2.67, -155.14, 0.05, 26, -3, -162.73, 0.08262, 7, 8, 29.92, -12.26, 0.00179, 9, 18.13, -9.25, 0.11275, 10, 7.04, -7.67, 0.62291, 11, -5.31, -8.95, 0.0289, 12, -17.14, -9.33, 0.00228, 25, -1.89, -128.47, 0.11, 26, -1.89, -181.39, 0.12136, 8, 7, 35.38, -18.72, 0.00679, 8, 25.73, -12.67, 0.05671, 9, 14.07, -10.36, 0.29702, 10, 3.44, -9.85, 0.41286, 11, -8.33, -11.88, 0.0162, 12, -20.08, -12.34, 0.00128, 25, -2.72, -147.05, 0.09914, 26, -3.05, -154.64, 0.11, 6, 7, 30.06, -17.73, 0.01439, 8, 20.33, -13.04, 0.11813, 9, 8.81, -11.63, 0.49579, 10, -1.27, -12.52, 0.13496, 25, -4.01, -119.08, 0.11, 26, -4.01, -172.01, 0.12673, 7, 7, 24.8, -19.09, 0.11469, 8, 15.58, -15.68, 0.26032, 9, 4.57, -15.02, 0.33535, 10, -4.4, -16.96, 0.06479, 13, -7.59, -35.67, 3e-05, 25, -7.12, -137.42, 0.09, 26, -7.46, -145.01, 0.13482, 7, 7, 18.3, -19.49, 0.20259, 8, 9.39, -17.7, 0.38346, 9, -1.2, -18.05, 0.18681, 10, -9.11, -21.46, 0.00108, 13, -6.42, -42.04, 6e-05, 25, -10.16, -108.9, 0.086, 26, -10.16, -161.83, 0.14, 6, 7, 5.52, -20.06, 0.51373, 8, -2.84, -21.46, 0.18167, 9, -12.62, -23.81, 0.02112, 13, -11.4, -30.26, 0.03339, 25, -15.6, -97.33, 0.09, 26, -15.6, -150.26, 0.16009, 6, 7, -5.87, -19.56, 0.52318, 8, -13.99, -23.83, 0.04943, 9, -23.22, -28.02, 0.00093, 13, -14.89, -19.41, 0.1648, 25, -19.52, -86.62, 0.1, 26, -19.52, -139.55, 0.16165, 5, 7, -16.22, -19.52, 0.36749, 8, -24.02, -26.39, 0.00978, 13, -18.45, -9.69, 0.32543, 25, -23.46, -77.05, 0.12, 26, -23.46, -129.98, 0.1773, 5, 7, -26.24, -18.4, 0.2164, 8, -34, -27.82, 0.00072, 13, -20.88, 0.09, 0.48558, 25, -26.28, -67.37, 0.12, 26, -26.28, -120.3, 0.1773, 4, 7, -37.39, -14.59, 0.0642, 13, -21.19, 11.87, 0.7108, 25, -27.06, -55.62, 0.095, 26, -27.06, -108.55, 0.13, 4, 7, -42.9, -13.51, 0.01836, 13, -22.09, 17.42, 0.77012, 25, -28.18, -50.11, 0.09, 26, -28.18, -103.04, 0.12152, 4, 7, -49.4, -10.2, 0.00066, 13, -21.25, 24.66, 0.90716, 25, -27.62, -42.84, 0.04, 26, -27.62, -95.77, 0.05218, 3, 13, -17.81, 27.64, 0.8947, 25, -24.3, -39.72, 0.0453, 26, -24.3, -92.65, 0.06, 3, 13, -12.04, 29.57, 0.89438, 25, -18.61, -37.57, 0.04, 26, -18.61, -90.5, 0.06562, 2, 10, 21.16, 4.6, 0.09897, 11, 5.71, 6.16, 0.90103, 3, 13, -7.71, 25.86, 0.84892, 25, -14.14, -41.1, 0.07108, 26, -14.14, -94.03, 0.08, 5, 7, -3.62, 13.78, 0.61875, 8, -20.17, 9, 0.02846, 13, 17.15, -9.93, 0.13559, 25, 12.12, -75.88, 0.13, 26, 12.12, -128.81, 0.0872, 6, 7, 11.22, 14.87, 0.56007, 8, -6.09, 13.79, 0.245, 9, -21.73, 10.39, 0.00696, 13, 23.34, -23.45, 0.00983, 25, 18.84, -89.15, 0.1, 26, 18.84, -142.08, 0.07814, 3, 7, 24.62, 11.17, 0.15183, 8, 7.81, 13.57, 0.65198, 9, -7.99, 12.51, 0.19619, 4, 7, 38.23, 3.86, 0.00057, 8, 22.83, 9.91, 0.12601, 9, 7.42, 11.41, 0.76263, 10, -8.95, 9.24, 0.11079, 2, 9, 19.89, 5.75, 0.1314, 10, 4.6, 7.24, 0.8686, 2, 10, 14.9, 3.13, 0.54048, 11, -0.06, 3.33, 0.45952, 2, 10, 21.62, -0.67, 0.00012, 11, 7.34, 1.13, 0.99988, 4, 7, -38.06, -11.26, 0.04648, 13, -18.3, 13.66, 0.77043, 25, -24.24, -53.72, 0.0831, 26, -24.24, -106.64, 0.1, 4, 7, -44.32, -4.66, 0.00107, 13, -14.29, 21.83, 0.82551, 25, -20.55, -45.4, 0.08, 26, -20.55, -98.32, 0.09342, 4, 7, -42.14, -8.46, 0.01162, 13, -17.09, 18.45, 0.81543, 25, -23.22, -48.88, 0.08, 26, -23.22, -101.81, 0.09295, 5, 7, -10.8, 11.24, 0.51652, 8, -26.49, 4.75, 0.00468, 13, 12.28, -4.07, 0.27079, 25, 7.02, -70.22, 0.12, 26, 7.02, -123.15, 0.088, 5, 7, -7.08, 5.84, 0.61827, 8, -21.53, 0.45, 0.00053, 13, 8.51, -9.45, 0.1732, 25, 3.46, -75.74, 0.12, 26, 3.46, -128.67, 0.088, 4, 7, -3.18, 1.61, 0.7286, 13, 5.9, -14.57, 0.0634, 25, 1.06, -80.97, 0.12, 26, 1.06, -133.89, 0.088, 5, 7, -1.1, -3.38, 0.75039, 8, -13.43, -6.98, 0.00542, 13, 1.94, -18.26, 0.03619, 25, -2.75, -84.81, 0.12, 26, -2.75, -137.73, 0.088, 6, 7, -1.12, -8.26, 0.68515, 8, -12.22, -11.7, 0.03498, 9, -23.51, -15.77, 0.00034, 13, -2.64, -19.93, 0.07152, 25, -7.26, -86.66, 0.12, 26, -7.26, -139.59, 0.088, 6, 7, -4.23, -13.1, 0.58105, 8, -14.02, -17.16, 0.04611, 9, -24.37, -21.45, 0.00074, 13, -8.26, -18.7, 0.13631, 25, -12.92, -85.65, 0.11579, 26, -12.92, -138.58, 0.12, 6, 7, -8.9, -17.33, 0.49372, 8, -17.48, -22.43, 0.03127, 9, -26.89, -27.22, 0.00016, 13, -13.85, -15.8, 0.21463, 25, -18.62, -82.97, 0.10023, 26, -18.62, -135.9, 0.16, 3, 13, -11.93, 24.81, 0.86331, 25, -18.32, -42.32, 0.057, 26, -18.32, -95.25, 0.07969, 4, 7, -34.64, -12.81, 0.08619, 13, -18.57, 9.91, 0.67941, 25, -24.36, -57.47, 0.1044, 26, -24.36, -110.4, 0.13, 5, 7, -27.72, -15.19, 0.19847, 8, -36.24, -25.09, 0.00025, 13, -18.39, 2.6, 0.57064, 25, -23.89, -64.77, 0.1, 26, -23.89, -117.7, 0.13064, 5, 7, -19.35, -16.31, 0.3352, 8, -27.85, -24.07, 0.00499, 13, -16.53, -5.64, 0.40741, 25, -21.71, -72.93, 0.11, 26, -21.71, -125.86, 0.1424, 5, 7, -12.43, -15.15, 0.45047, 8, -21.44, -21.21, 0.01667, 13, -13.03, -11.73, 0.2797, 25, -17.97, -78.88, 0.11316, 26, -17.97, -131.8, 0.14, 6, 7, -8.06, -11.74, 0.53845, 8, -18.07, -16.81, 0.02303, 9, -28.42, -21.78, 4e-05, 13, -8.32, -14.63, 0.19906, 25, -13.14, -81.59, 0.12, 26, -13.14, -134.52, 0.11943, 5, 7, -5.95, -7.14, 0.63056, 8, -17.18, -11.83, 0.01255, 13, -3.27, -15.02, 0.14889, 25, -8.09, -81.78, 0.12, 26, -8.09, -134.7, 0.088, 5, 7, -7.51, -2.22, 0.62603, 8, -19.92, -7.46, 0.0008, 13, 0.8, -11.85, 0.16518, 25, -4.15, -78.45, 0.12, 26, -4.15, -131.37, 0.088, 4, 7, -9.89, 1.99, 0.56334, 13, 3.92, -8.15, 0.22866, 25, -1.17, -74.63, 0.12, 26, -1.17, -127.55, 0.088, 5, 7, -11.35, 6.84, 0.49106, 8, -25.92, 0.35, 0.00034, 13, 7.96, -5.09, 0.26655, 25, 2.74, -71.41, 0.14, 26, 2.74, -124.34, 0.10204, 4, 7, -33.82, -9.8, 0.02518, 13, -15.46, 10.18, 0.56597, 25, -21.26, -57.07, 0.2, 26, -21.26, -110, 0.20886, 4, 7, -29.27, -12.17, 0.07028, 13, -16.1, 5.1, 0.5241, 25, -21.7, -62.18, 0.19562, 26, -21.7, -115.11, 0.21, 5, 7, -24.31, -12.27, 0.1257, 8, -33.66, -21.41, 3e-05, 13, -14.47, 0.41, 0.46865, 25, -19.89, -66.8, 0.19562, 26, -19.89, -119.73, 0.21, 5, 7, -21.7, -9.61, 0.15515, 8, -31.81, -18.18, 9e-05, 13, -11.07, -1.11, 0.47182, 25, -16.43, -68.18, 0.19, 26, -16.43, -121.11, 0.18295, 4, 7, -21.49, -6.06, 0.13537, 13, -7.66, -0.07, 0.48159, 25, -13.07, -67.01, 0.20305, 26, -13.07, -119.94, 0.18, 4, 7, -25.49, -2.92, 0.05126, 13, -6.11, 4.77, 0.57322, 25, -11.71, -62.12, 0.20552, 26, -11.71, -115.04, 0.17, 4, 7, -29.99, -1.65, 0.00483, 13, -6.49, 9.43, 0.63355, 25, -12.27, -57.47, 0.2, 26, -12.27, -110.4, 0.16162, 4, 7, -33.38, -2.75, 0.00272, 13, -8.7, 12.23, 0.62175, 25, -14.58, -54.76, 0.20552, 26, -14.58, -107.69, 0.17, 4, 7, -35, -6.25, 0.00915, 13, -12.54, 12.54, 0.61791, 25, -18.43, -54.61, 0.19, 26, -18.43, -107.54, 0.18295, 4, 7, -32.9, -6.63, 0.01773, 13, -12.17, 10.43, 0.60933, 25, -17.98, -56.7, 0.19, 26, -17.98, -109.63, 0.18295, 4, 7, -31.78, -8.84, 0.03365, 13, -13.85, 8.61, 0.56826, 25, -19.59, -58.59, 0.1981, 26, -19.59, -111.51, 0.2, 4, 7, -29.24, -10.23, 0.0613, 13, -14.26, 5.74, 0.5406, 25, -19.89, -61.46, 0.1981, 26, -19.89, -114.39, 0.2, 4, 7, -24.5, -8.67, 0.10829, 13, -11.16, 1.85, 0.51876, 25, -16.63, -65.24, 0.19, 26, -16.63, -118.16, 0.18295, 4, 7, -24.5, -6.14, 0.08919, 13, -8.79, 2.72, 0.52777, 25, -14.3, -64.27, 0.20305, 26, -14.3, -117.2, 0.18, 4, 7, -29.9, -3.44, 0.01504, 13, -8.13, 8.72, 0.60191, 25, -13.88, -58.25, 0.20305, 26, -13.88, -111.17, 0.18, 4, 7, -31.73, -4.46, 0.01239, 13, -9.73, 10.08, 0.60456, 25, -15.53, -56.95, 0.20305, 26, -15.53, -109.87, 0.18, 4, 7, -26.06, -10.39, 0.09525, 13, -13.31, 2.71, 0.50522, 25, -18.82, -64.46, 0.2, 26, -18.82, -117.39, 0.19952, 4, 7, -26.97, -4.44, 0.04521, 13, -8.05, 5.63, 0.57175, 25, -13.68, -61.33, 0.20305, 26, -13.68, -114.26, 0.18, 4, 7, -28.79, -7.05, 0.04681, 13, -11.13, 6.43, 0.57014, 25, -16.79, -60.66, 0.20305, 26, -16.79, -113.58, 0.18, 5, 7, 25.51, 4.38, 0.02314, 8, 10.38, 7.22, 0.62498, 9, -4.39, 6.68, 0.17988, 25, 14.65, -106.39, 0.092, 26, 14.65, -159.31, 0.08, 6, 7, 7.74, 7.97, 0.7329, 8, -7.72, 6.23, 0.08676, 9, -22.07, 2.67, 2e-05, 13, 15.65, -22.6, 0.00831, 25, 11.12, -88.6, 0.092, 26, 11.12, -141.53, 0.08, 5, 8, 25.85, 1.62, 0.00102, 9, 11.79, 3.75, 0.66339, 10, -2.64, 3.08, 0.1636, 25, 11.28, -122.48, 0.092, 26, 11.28, -175.41, 0.08, 1, 10, 10.14, 1.39, 1, 2, 11, 5.95, -1.04, 0.93663, 12, -6.09, -1.13, 0.06337, 6, 7, 10.01, -6.93, 0.58831, 8, -1.79, -7.62, 0.22869, 9, -13.91, -9.99, 0.01005, 13, 2.48, -29.9, 0.00095, 25, -1.75, -96.42, 0.092, 26, -1.75, -149.35, 0.08, 6, 7, 23.65, -7.27, 0.02136, 8, 11.5, -4.53, 0.56401, 9, -1.32, -4.72, 0.24215, 10, -12.91, -8.67, 0.00047, 25, 3.17, -109.14, 0.092, 26, 3.17, -162.07, 0.08, 6, 7, 34.31, -12.21, 0.00083, 8, 23.07, -6.63, 0.0208, 9, 10.43, -4.85, 0.59932, 10, -1.58, -5.56, 0.18904, 25, 2.72, -120.89, 0.09, 26, 2.72, -173.82, 0.1, 7, 8, 32.16, -10.22, 3e-05, 9, 20, -6.86, 0.04447, 10, 8.18, -4.85, 0.7409, 11, -4.83, -5.95, 0.05083, 12, -16.74, -6.32, 0.00439, 25, 0.45, -130.4, 0.06939, 26, 0.45, -183.33, 0.09, 5, 10, 17.07, -5.27, 0.05849, 11, 3.93, -4.37, 0.66162, 12, -8.02, -4.51, 0.15531, 25, -2.63, -138.75, 0.04, 26, -2.63, -191.68, 0.08458, 4, 7, -38.49, 2.5, 0.00327, 13, -5.55, 18.84, 0.69232, 25, -11.7, -48.03, 0.16, 26, -11.7, -100.96, 0.14441, 4, 7, -28.12, 4.94, 0.21322, 13, 0.34, 9.98, 0.4658, 25, -5.46, -56.66, 0.18, 26, -5.46, -109.58, 0.14097, 5, 7, -18.88, 6.27, 0.37799, 8, -33.06, -2.09, 8e-05, 13, 4.81, 1.77, 0.30095, 25, -0.68, -64.68, 0.18, 26, -0.68, -117.61, 0.14097, 4, 7, -30.35, -2.65, 0.00148, 13, -11.36, 21.75, 0.73455, 25, -13.16, -80.19, 0.14, 26, -13.49, -87.78, 0.12397, 4, 7, -29.05, -7.13, 0.01051, 13, -15.1, 18.97, 0.67396, 25, -16.8, -83.12, 0.15586, 26, -17.13, -90.71, 0.15966], "hull": 32, "edges": [12, 14, 22, 24, 24, 26, 14, 16, 16, 18, 18, 20, 20, 22, 52, 54, 2, 4, 48, 46, 46, 44, 28, 26, 60, 62, 54, 56, 58, 60, 58, 56, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 48, 50, 50, 52, 100, 50, 102, 66, 2, 0, 0, 62, 66, 0, 102, 84, 84, 86, 86, 82, 104, 54, 104, 82, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 88, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 122, 140, 142, 142, 144, 146, 148, 150, 152, 152, 140, 140, 138, 142, 122, 144, 124, 144, 154, 154, 146, 154, 126, 146, 128, 148, 130, 148, 156, 156, 150, 156, 132, 150, 134, 152, 136, 158, 152, 158, 150, 158, 156, 158, 148, 158, 146, 158, 154, 158, 144, 158, 142, 158, 140, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 72, 160, 70, 162, 164, 74, 166, 76, 162, 160, 166, 164, 164, 160, 166, 168, 168, 80, 166, 78, 46, 170, 170, 162, 44, 172, 172, 160, 170, 172, 40, 174, 174, 164, 172, 174, 36, 176, 176, 166, 174, 176, 32, 178, 178, 168, 176, 178, 4, 6, 6, 8, 8, 10, 10, 12, 170, 96, 162, 90, 180, 182, 182, 184, 184, 120, 180, 102, 36, 38, 38, 40, 40, 42, 42, 44, 32, 34, 34, 36, 28, 30, 30, 32], "width": 55, "height": 105}}, "VayCa": {"VayCa": {"type": "mesh", "uvs": [0.69292, 0.15554, 0.35692, 0.12222, 0.02792, 0, 0.01742, 0.33642, 0.12942, 0.65058, 0.26242, 0.91238, 0.55292, 0.99999, 0.89927, 0.71455, 1, 0.63154, 1, 0.40306, 0.99999, 0.3745], "triangles": [3, 2, 1, 4, 3, 1, 5, 4, 6, 1, 6, 4, 9, 0, 10, 9, 7, 0, 8, 7, 9, 6, 1, 7, 1, 0, 7], "vertices": [2, 29, 1.19, -4.91, 0.98813, 30, -7.26, -5.86, 0.01187, 2, 29, 12.59, -6.01, 0.19832, 30, 4.19, -5.53, 0.80168, 1, 30, 15.63, -7.43, 1, 1, 30, 15.13, 0.97, 1, 2, 29, 20.63, 7.02, 0.01639, 30, 10.54, 8.4, 0.98361, 2, 29, 16.26, 13.67, 0.27369, 30, 5.38, 14.45, 0.72631, 2, 29, 6.44, 16.08, 0.80352, 30, -4.67, 15.62, 0.19648, 1, 29, -5.5, 9.22, 1, 1, 29, -8.98, 7.23, 1, 1, 29, -9.11, 1.52, 1, 1, 29, -9.12, 0.81, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 0, 16, 18, 18, 20, 12, 2, 12, 14, 14, 16, 0, 14], "width": 34, "height": 25}}, "VayCa2": {"VayCa2": {"x": 1.82, "y": 8.23, "rotation": 67.38, "width": 29, "height": 25}}, "efect_light2 copy 10": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}, "efect_light2 copy 11": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}, "efect_light2 copy 12": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}, "efect_light2 copy 13": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}, "efect_light2 copy 7": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}, "efect_light2 copy 8": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}, "efect_light2 copy 9": {"efect_light2 copy 7": {"y": 0.5, "width": 54, "height": 53}}}}, "animations": {"animation": {"slots": {"EffNuoc": {"attachment": [{"time": 0, "name": null}, {"time": 0.6333, "name": "Water/Water 02"}, {"time": 0.6667, "name": "Water/Water 03"}, {"time": 0.7, "name": "Water/Water 05"}, {"time": 0.7667, "name": "Water/Water 06"}, {"time": 0.8, "name": "Water/Water 07"}, {"time": 0.8333, "name": "Water/Water 08"}, {"time": 0.8667, "name": "Water/Water 09"}, {"time": 0.9, "name": "Water/Water 10"}, {"time": 0.9333, "name": "Water/Water 11"}, {"time": 0.9667, "name": "Water/Water 12"}, {"time": 1, "name": "Water/Water 13"}, {"time": 1.0333, "name": "Water/Water 14"}, {"time": 1.0667, "name": "Water/Water 15"}, {"time": 1.1333, "name": null}]}, "EffNuoc2": {"attachment": [{"time": 0, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00008"}, {"time": 0.0333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00009"}, {"time": 0.0667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00010"}, {"time": 0.1, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00011"}, {"time": 0.1333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00012"}, {"time": 0.1667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00013"}, {"time": 0.2667, "name": null}, {"time": 1.4333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00000"}, {"time": 1.4667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00001"}, {"time": 1.5, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00002"}, {"time": 1.5333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00003"}, {"time": 1.5667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00004"}, {"time": 1.6, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00005"}, {"time": 1.6333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00006"}, {"time": 1.6667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00008"}]}, "EffNuoc3": {"attachment": [{"time": 0, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00010"}, {"time": 0.0333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00011"}, {"time": 0.0667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00012"}, {"time": 0.1, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00013"}, {"time": 0.2, "name": null}, {"time": 1.3667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00000"}, {"time": 1.4, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00001"}, {"time": 1.4333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00002"}, {"time": 1.4667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00003"}, {"time": 1.5, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00004"}, {"time": 1.5333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00005"}, {"time": 1.5667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00006"}, {"time": 1.6, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00007"}, {"time": 1.6333, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00008"}, {"time": 1.6667, "name": "Water2/Elements - Liquid 020 Splash Right noCT noRSZ_00010"}]}, "efect_light2 copy 7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff00"}]}, "efect_light2 copy 8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00"}]}, "efect_light2 copy 9": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "color": "ffffff00"}]}, "efect_light2 copy 10": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffff00"}]}, "efect_light2 copy 11": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00"}]}, "efect_light2 copy 12": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00"}]}, "efect_light2 copy 13": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}]}}, "bones": {"Fish": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -9.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 40.85, "curve": [0.29, 0, 0.629, 0.38]}, {"time": 0.5, "angle": 152.41, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.5667, "angle": 177.94, "curve": [0.255, 0, 0.62, 0.47]}, {"time": 0.6667, "angle": -160.65, "curve": [0.332, 0.33, 0.668, 0.67]}, {"time": 0.7, "angle": -155.07, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 0.8, "angle": -157.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -50.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -13.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 9.17, "y": 16.86}, {"time": 0.3667, "x": 17.06, "y": 50.37}, {"time": 0.4, "x": 8.8, "y": 47.2}, {"time": 0.5, "x": -17.83, "y": 21.01}, {"time": 0.5667, "x": -13.15, "y": 3.63}, {"time": 0.6667, "x": -3.65, "y": -18.45}, {"time": 0.8, "x": -3.25, "y": -41.02}, {"time": 1.1, "x": 16.07, "y": -39.96}, {"time": 1.4, "x": 8.58, "y": -18.02}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3667, "x": 0.965, "y": 0.965}, {"time": 1.1, "x": 0.502, "y": 0.502}, {"time": 1.4, "x": 1, "y": 1}]}, "Head3D": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 106.13, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -483.32, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 106.13, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 276.89, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}]}, "EffNuoc": {"translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "VayCa1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0333, "angle": -11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 75.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 102.59, "curve": [0.294, 0, 0.632, 0.37]}, {"time": 0.4333, "angle": 92.38, "curve": [0.305, 0.23, 0.645, 0.59]}, {"time": 0.6667, "angle": 89.67, "curve": [0.355, 0.41, 0.756, 1]}, {"time": 1.4, "angle": 59.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0}]}, "Fish7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -3.96}, {"time": 0.3667, "angle": -16.9}, {"time": 0.5333, "angle": 19.54}, {"time": 1.0667, "angle": 11.64}, {"time": 1.4333, "angle": -13.96}]}, "VayCa2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0333, "angle": 52.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -31.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -39.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -55.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0}]}, "EffNuoc2": {"translate": [{"time": 0, "x": 8.25, "y": -4.79}]}, "DauCa": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 19.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 17.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -13.38}, {"time": 1.0667, "angle": -24.74}, {"time": 1.3333, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0}]}, "Fish6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 4.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -10.08, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 0.4333, "angle": 10.23, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.5, "angle": -0.52, "curve": [0.265, 0, 0.618, 0.43]}, {"time": 0.5667, "angle": 4.27, "curve": [0.325, 0.31, 0.675, 0.69]}, {"time": 0.7333, "angle": 1.08, "curve": [0.293, 0.18, 0.755, 1]}, {"time": 1, "angle": 29.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -26.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0}]}, "Fish5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 21.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 0.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -3.24, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 0.4333, "angle": 31.04, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.5, "angle": 8.74, "curve": [0.265, 0, 0.618, 0.43]}, {"time": 0.5667, "angle": 10.39, "curve": [0.325, 0.31, 0.675, 0.69]}, {"time": 0.7333, "angle": -4.96, "curve": [0.293, 0.18, 0.755, 1]}, {"time": 1, "angle": 16.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -38.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0}]}, "Fish4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 21.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 0.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 1.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.73, "curve": [0.265, 0, 0.618, 0.43]}, {"time": 0.5667, "angle": 3.73, "curve": [0.351, 0.4, 0.757, 1]}, {"time": 0.7, "angle": 4.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -40.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0}]}, "Fish3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 21.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 0.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 1.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 21.3, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.6, "angle": 12.76, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 0.7667, "angle": 8.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -1.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 41.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0}]}, "Fish2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.309, 0, 0.644, 0.35]}, {"time": 0.1667, "angle": -2.6, "curve": [0.326, 0.28, 0.659, 0.62]}, {"time": 0.2667, "angle": 4.31, "curve": [0.327, 0.3, 0.66, 0.63]}, {"time": 0.3333, "angle": -3.06, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 1.5333, "angle": 0}]}, "Fish12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4667, "angle": 28.14}, {"time": 0.8667, "angle": -22.97}, {"time": 1.0333, "angle": -13.25}, {"time": 1.2667, "angle": 18.66}, {"time": 1.4333, "angle": 0}]}, "Fish13": {"rotate": [{"time": 0.5333, "angle": 0}, {"time": 0.8667, "angle": 16.76}, {"time": 1.0333, "angle": -13.25}, {"time": 1.2667, "angle": 18.66}, {"time": 1.4333, "angle": 0}]}, "Fish14": {"rotate": [{"time": 0.8667, "angle": 0}, {"time": 1.0333, "angle": -13.25}, {"time": 1.2667, "angle": 18.66}, {"time": 1.4333, "angle": 0}]}, "Fish9": {"rotate": [{"time": 0, "angle": 29.37}, {"time": 0.5333, "angle": -4.86}, {"time": 0.8667, "angle": 0}, {"time": 1.0333, "angle": 5.55}, {"time": 1.2667, "angle": 19.45}]}, "Fish10": {"rotate": [{"time": 0.8667, "angle": 0}, {"time": 1.0333, "angle": 5.55}, {"time": 1.2667, "angle": 19.45}]}, "Fish11": {"rotate": [{"time": 0.8667, "angle": 0}, {"time": 1.0333, "angle": 27.65}, {"time": 1.2667, "angle": 19.45}]}, "EffNuoc3": {"translate": [{"time": 0, "x": -16.57, "y": -14.56}]}, "Star": {"rotate": [{"time": 0.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Star2": {"rotate": [{"time": 0.5333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Star3": {"rotate": [{"time": 0.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Star4": {"rotate": [{"time": 0.8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Star5": {"rotate": [{"time": 0.9333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Star6": {"rotate": [{"time": 1.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Star7": {"rotate": [{"time": 1.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -117.3}], "translate": [{"time": 0, "x": -0.4, "y": 0.85}]}, "Nuoc2": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.18, "y": -8.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -0.15, "y": 6.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0.04, "y": -1.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}]}, "Nuoc3": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 8.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -4.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0, "y": 4.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}]}, "VayCa3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 7.84}, {"time": 0.6667, "angle": 10.14}, {"time": 1.6667, "angle": 0}]}, "VayCa4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 7.84}, {"time": 0.6667, "angle": 10.14}, {"time": 1.6667, "angle": 0}]}, "AllFish": {"translate": [{"time": 0, "x": -0.4, "y": 0.85}]}}}}}