{"skins": {"default": {"11": {"A": {"x": 0.5, "width": 113, "height": 108}, "Q": {"width": 106, "height": 102}, "J": {"x": 0.5, "width": 105, "y": 0.5, "height": 101}, "K": {"x": 0.5, "width": 111, "y": 0.5, "height": 107}, "10": {"x": 0.5, "width": 133, "y": 0.5, "height": 101}}, "jackpot7": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.41, -6.94, -23.41, 7.06, 25.59, 7.06, 25.59, -6.94], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "jackpot6": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.41, -6.94, -23.41, 7.06, 25.59, 7.06, 25.59, -6.94], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "jackpot9": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.41, -6.94, -23.41, 7.06, 25.59, 7.06, 25.59, -6.94], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "jackpot8": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.41, -6.94, -23.41, 7.06, 25.59, 7.06, 25.59, -6.94], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "binhtiendon4": {"binhtiendon4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-93, -93, -93, 93, 93, 93, 93, -93], "width": 186, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 186}}, "jackpot10": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.41, -6.94, -23.41, 7.06, 25.59, 7.06, 25.59, -6.94], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "quadao1": {"quadao1": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 32, -8, -44.59, 1, 1, 32, -8, 50.41, 1, 1, 32, 87, 50.41, 1, 1, 32, 87, -44.59, 1], "width": 95, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 95}}, "jackpot12": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-16.09, -14.99, -15.61, 16.01, 16.39, 15.51, 15.9, -15.49], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "jackpot11": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-16.09, -14.99, -15.61, 16.01, 16.39, 15.51, 15.9, -15.49], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "vongkimco2": {"vongkimco": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-70.71, -61.29, -70.71, 59.71, 3.29, 59.71, 3.29, -61.29], "width": 121, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 74}}, "quadao0": {"quadao0": {"triangles": [12, 13, 17, 16, 14, 15, 38, 6, 7, 38, 7, 8, 22, 8, 9, 38, 8, 22, 10, 11, 19, 19, 11, 18, 13, 16, 17, 20, 9, 10, 13, 14, 16, 21, 22, 9, 21, 9, 20, 6, 32, 5, 4, 5, 33, 4, 33, 3, 5, 32, 33, 34, 2, 3, 23, 38, 22, 37, 38, 23, 18, 12, 17, 20, 10, 19, 11, 12, 18, 3, 33, 34, 35, 1, 2, 34, 35, 2, 31, 32, 6, 37, 6, 38, 0, 1, 35, 23, 36, 37, 24, 36, 23, 6, 37, 31, 37, 36, 31, 30, 36, 24, 30, 31, 36, 25, 30, 24, 26, 29, 30, 25, 26, 30, 27, 28, 29, 26, 27, 29], "uvs": [0, 0.70348, 0.01524, 0.60909, 0.05774, 0.49174, 0.11781, 0.32592, 0.21446, 0.17909, 0.30125, 0.14763, 0.3762, 0.17909, 0.47088, 0.27872, 0.5123, 0.37311, 0.54386, 0.30494, 0.58528, 0.14238, 0.66616, 0.02177, 0.74111, 0, 0.8496, 0.0008, 0.94427, 0.09519, 1, 0.21055, 0.93441, 0.32592, 0.85749, 0.48323, 0.79437, 0.57238, 0.69377, 0.59336, 0.61093, 0.57762, 0.55373, 0.49897, 0.52475, 0.43846, 0.52152, 0.55007, 0.49487, 0.72822, 0.44563, 0.85485, 0.38831, 0.91709, 0.32372, 1, 0.29466, 1, 0.28981, 0.88275, 0.29627, 0.78188, 0.23491, 0.78617, 0.17598, 0.74324, 0.13723, 0.69173, 0.09686, 0.6488, 0.05084, 0.65095, 0.30294, 0.65908, 0.33415, 0.51815, 0.38567, 0.41542], "vertices": [1, 33, -9.98, 59.83, 1, 1, 33, -6.13, 58.13, 1, 1, 33, -1.37, 53.45, 1, 1, 33, 5.37, 46.84, 1, 1, 33, 11.28, 36.24, 1, 1, 33, 12.47, 26.77, 1, 1, 33, 11.1, 18.61, 1, 1, 33, 6.91, 8.33, 1, 1, 33, 2.99, 3.86, 1, 1, 33, 5.75, 0.39, 1, 1, 33, 12.37, -4.19, 1, 1, 33, 17.22, -13.06, 1, 1, 33, 18.03, -21.24, 1, 1, 33, 17.88, -33.06, 1, 1, 33, 13.91, -43.34, 1, 1, 33, 9.11, -49.37, 1, 1, 33, 4.46, -42.17, 1, 1, 33, -1.91, -33.72, 1, 1, 33, -5.49, -26.8, 1, 1, 33, -6.24, -15.83, 1, 1, 33, -5.5, -6.81, 1, 1, 33, -2.21, -0.6, 1, 1, 33, 0.3, 2.53, 1, 1, 33, -4.27, 2.93, 1, 1, 33, -11.55, 5.91, 1, 1, 33, -16.68, 11.33, 1, 1, 33, -19.17, 17.6, 1, 1, 33, -22.5, 24.68, 1, 1, 33, -22.47, 27.84, 1, 1, 33, -17.65, 28.32, 1, 1, 33, -13.53, 27.58, 1, 1, 33, -13.63, 34.27, 1, 1, 33, -11.81, 40.67, 1, 1, 33, -9.65, 44.87, 1, 1, 33, -7.85, 49.26, 1, 1, 33, -7.89, 54.27, 1, 1, 33, -8.5, 26.8, 1, 1, 33, -2.76, 23.34, 1, 1, 33, 1.4, 17.68, 1], "width": 109, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 66, 8, 68, 6, 2, 4, 4, 6, 70, 4, 64, 10, 62, 12, 60, 72, 72, 74, 74, 76, 76, 14, 26, 34, 24, 36, 22, 38, 20, 40, 18, 42], "type": "mesh", "hull": 36, "height": 41}}, "lotus3": {"lotus3": {"triangles": [6, 4, 5, 7, 2, 6, 6, 3, 4, 6, 2, 3, 7, 8, 2, 8, 9, 2, 9, 1, 2, 9, 0, 1], "uvs": [0, 0.0663, 0.13848, 0, 0.45655, 0.18494, 0.83074, 0.29918, 1, 0.59797, 1, 1, 0.55945, 1, 0.22268, 0.92751, 0.0309, 0.60676, 0, 0.27721], "vertices": [1, 16, -10.78, 3.12, 1, 1, 16, -9.47, 7.76, 1, 3, 16, 1.7, 10.86, 0.64524, 17, 0.45, 10.89, 0.2393, 18, -8.22, 12, 0.11546, 3, 16, 12.34, 16.83, 0.10442, 17, 11.76, 15.46, 0.17722, 18, 3.57, 15.13, 0.71836, 3, 16, 23.14, 14, 0.00646, 17, 22.12, 11.28, 0.00674, 18, 13.33, 9.7, 0.9868, 1, 18, 20.53, -1.44, 1, 2, 17, 20.18, -7.67, 0.03271, 18, 9.07, -8.86, 0.96729, 3, 16, 14.79, -11.09, 0.03755, 17, 10.65, -12.55, 0.45026, 18, -1, -12.52, 0.51218, 3, 16, 2.98, -8.27, 0.70913, 17, -0.71, -8.26, 0.27872, 18, -11.74, -6.86, 0.01215, 1, 16, -5.66, -1.6, 1], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 33}}, "vongkimco": {"vongkimco": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-70.71, -61.29, -70.71, 59.71, 3.29, 59.71, 3.29, -61.29], "width": 121, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 74}}, "lotus4": {"lotus4": {"triangles": [4, 10, 9, 9, 3, 4, 10, 4, 5, 8, 3, 9, 11, 10, 19, 39, 3, 8, 9, 39, 8, 11, 9, 10, 13, 12, 11, 17, 13, 11, 39, 12, 38, 9, 11, 39, 12, 39, 11, 37, 38, 12, 15, 36, 37, 15, 37, 12, 39, 38, 2, 3, 39, 2, 2, 38, 37, 2, 37, 36, 2, 36, 35, 35, 36, 34, 14, 12, 13, 15, 32, 34, 34, 36, 15, 33, 34, 32, 33, 32, 29, 33, 29, 1, 35, 34, 33, 2, 35, 33, 2, 33, 1, 21, 6, 7, 21, 7, 0, 20, 10, 5, 22, 21, 0, 19, 10, 20, 5, 6, 20, 21, 20, 6, 19, 17, 11, 20, 21, 22, 22, 19, 20, 23, 19, 22, 18, 17, 19, 23, 18, 19, 24, 18, 23, 18, 24, 17, 25, 26, 24, 24, 16, 17, 26, 16, 24, 23, 22, 0, 1, 23, 0, 24, 23, 1, 25, 24, 1, 26, 25, 1, 27, 15, 26, 28, 29, 27, 27, 26, 1, 28, 27, 1, 29, 28, 1, 14, 13, 17, 16, 14, 17, 15, 14, 16, 15, 16, 26, 30, 15, 27, 15, 12, 14, 27, 29, 30, 15, 30, 32, 30, 31, 32, 31, 30, 29, 32, 31, 29], "uvs": [1, 0.04249, 1, 1, 0, 1, 0, 0.09719, 0, 0, 0.81464, 0, 0.87934, 0, 1, 0, 0.05477, 0.14138, 0.13842, 0.11613, 0.20548, 0.12665, 0.25204, 0.33286, 0.29938, 0.42755, 0.41853, 0.44228, 0.48718, 0.49278, 0.55188, 0.66742, 0.57319, 0.5054, 0.63473, 0.39388, 0.70575, 0.3518, 0.74283, 0.18557, 0.77518, 0.0488, 0.93773, 0.03617, 0.95667, 0.1498, 0.92668, 0.32234, 0.89907, 0.53697, 0.89433, 0.66532, 0.84936, 0.71792, 0.81543, 0.83786, 0.76493, 0.94938, 0.67895, 0.95569, 0.61816, 0.90519, 0.55578, 0.94938, 0.48876, 0.95359, 0.38305, 0.96831, 0.32384, 0.94728, 0.23468, 0.9599, 0.1534, 0.90099, 0.08318, 0.76632, 0.04846, 0.57484, 0.03426, 0.34759], "vertices": [1, 21, 33.56, -12.51, 1, 2, 20, 29.67, -32.13, 0.43448, 21, -7.47, -33.22, 0.56552, 2, 23, 35.18, 26.98, 0.09029, 24, 7.37, 31.76, 0.90971, 1, 24, 38.63, 1.75, 1, 1, 24, 42, -1.48, 1, 6, 19, 44.46, 29.41, 0.01358, 20, 34.23, 21.22, 0.03696, 21, 24.68, 9.59, 0.94662, 22, -32.49, -51.45, 0.00225, 23, -38.58, -60.98, 0.00057, 24, -30.21, -76.7, 2e-05, 5, 19, 52.42, 27.12, 0.00067, 20, 41.32, 16.93, 0.00164, 21, 28.42, 2.2, 0.99764, 22, -40.55, -53.32, 4e-05, 23, -46.07, -64.52, 0, 1, 21, 35.38, -11.59, 1, 3, 19, -50.88, 49.82, 0.0002, 22, 60.73, -22.86, 0.00028, 24, 32.25, -1.84, 0.99952, 6, 19, -40.25, 48.02, 0.00667, 20, -42.7, 61.27, 0.00037, 21, -19.31, 84.34, 7e-05, 22, 50.57, -26.46, 0.01183, 23, 37.29, -18.94, 0.0171, 24, 25.71, -10.4, 0.96396, 6, 19, -32.15, 45.16, 0.02591, 20, -35.62, 56.39, 0.0029, 21, -15.89, 76.45, 0.0014, 22, 42.09, -27.91, 0.04682, 23, 29.31, -22.16, 0.07757, 24, 19.4, -16.25, 0.84541, 6, 19, -29.16, 34, 0.03466, 20, -35.65, 44.84, 0.0043, 21, -22.04, 66.67, 0.00246, 22, 34.05, -19.61, 0.08059, 23, 19.7, -15.75, 0.21685, 24, 8.13, -13.69, 0.66113, 6, 19, -24.59, 27.95, 0.05175, 20, -32.82, 37.81, 0.00653, 21, -23.36, 59.21, 0.00416, 22, 27.12, -16.56, 0.16182, 23, 12.28, -14.24, 0.42086, 24, 0.66, -14.91, 0.35488, 6, 19, -10.14, 23.05, 0.22104, 20, -20.14, 29.31, 0.03292, 21, -17.12, 45.28, 0.0225, 22, 12.11, -19.31, 0.42745, 23, -1.81, -20.11, 0.197, 24, -10.41, -25.43, 0.09909, 6, 19, -2.36, 18.29, 0.41964, 20, -13.87, 22.69, 0.05977, 21, -15.32, 36.34, 0.03575, 22, 3, -18.94, 0.37213, 23, -10.79, -21.68, 0.07509, 24, -18.25, -30.09, 0.03761, 6, 19, 3.27, 7.94, 0.80931, 20, -11.13, 11.23, 0.03763, 21, -19.07, 25.17, 0.01429, 22, -6.96, -12.64, 0.1265, 23, -21.86, -17.64, 0.00843, 24, -30.03, -30.26, 0.00385, 6, 19, 8.05, 14.66, 0.60487, 20, -4.77, 16.47, 0.17439, 21, -10.9, 26.24, 0.06754, 22, -7.86, -20.84, 0.12069, 23, -21.01, -25.84, 0.02098, 24, -26.31, -37.61, 0.01153, 6, 19, 17.1, 17.62, 0.32861, 20, 4.74, 16.97, 0.39733, 21, -2.57, 21.62, 0.20027, 22, -14.33, -27.83, 0.05269, 23, -25.84, -34.04, 0.01359, 24, -27.9, -47, 0.00752, 6, 19, 26.39, 17.04, 0.1032, 20, 13.56, 13.99, 0.43935, 21, 3.33, 14.42, 0.43548, 22, -22.73, -31.85, 0.01569, 23, -33.2, -39.75, 0.00449, 24, -32.74, -54.95, 0.00178, 6, 19, 33.16, 23.4, 0.0609, 20, 21.76, 18.36, 0.18532, 21, 12.59, 13.78, 0.73684, 22, -25.55, -40.7, 0.01216, 23, -34.08, -49, 0.00359, 24, -30.27, -63.91, 0.00118, 6, 19, 38.96, 28.56, 0.03601, 20, 28.7, 21.83, 0.09194, 21, 20.32, 13.04, 0.86253, 22, -28.1, -48.03, 0.00708, 23, -35.02, -56.7, 0.00201, 24, -28.41, -71.44, 0.00044, 1, 21, 30.24, -5.26, 1, 1, 21, 26.46, -9.88, 1, 2, 20, 38.49, 0.56, 0.00307, 21, 17.34, -10.19, 0.99693, 2, 20, 30.13, -6.43, 0.10681, 21, 6.55, -11.67, 0.89319, 2, 20, 26.42, -11.38, 0.31176, 21, 0.77, -13.91, 0.68824, 2, 20, 20.19, -10.56, 0.57161, 21, -4.07, -9.91, 0.42839, 3, 19, 33.42, -9.26, 0.0016, 20, 13.49, -13.24, 0.84462, 21, -11.17, -8.63, 0.15378, 3, 19, 25.73, -12.62, 0.07535, 20, 5.19, -14.47, 0.89558, 21, -18.86, -5.27, 0.02907, 2, 19, 15.07, -9.86, 0.61513, 20, -4.38, -9.03, 0.38487, 3, 19, 8.27, -5.38, 0.97834, 20, -9.78, -2.93, 0.02165, 21, -25.44, 12.46, 0, 3, 19, 0.01, -5.2, 0.91941, 21, -30.93, 18.63, 0, 22, -10.51, 0.43, 0.08059, 2, 19, -8.29, -3.02, 0.14874, 22, -2.2, 2.56, 0.85126, 2, 22, 10.82, 6.31, 1, 24, -25.49, -4.66, 0, 2, 22, 18.43, 7.04, 0.7125, 23, -1.22, 6.98, 0.2875, 3, 22, 29.41, 10.21, 0.05561, 23, 8.84, 12.41, 0.84537, 24, -12.04, 8.76, 0.09903, 2, 23, 19.46, 14.3, 0.448, 24, -2.8, 14.3, 0.552, 2, 23, 30.35, 12.29, 0.0951, 24, 8.09, 16.31, 0.9049, 2, 23, 38.29, 5.88, 0.00446, 24, 17.8, 13.15, 0.99554, 1, 24, 26.93, 6.91, 1], "width": 128, "edges": [2, 4, 4, 6, 6, 8, 6, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 8, 10, 40, 10, 10, 12, 12, 14, 12, 42, 2, 0, 0, 14, 42, 0, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 6], "type": "mesh", "hull": 8, "height": 48}}, "lotus1": {"lotus1": {"triangles": [35, 12, 13, 19, 20, 21, 12, 35, 11, 35, 14, 15, 13, 14, 35, 10, 11, 35, 47, 19, 21, 18, 19, 47, 17, 18, 47, 16, 36, 35, 16, 35, 15, 61, 36, 16, 10, 35, 36, 17, 61, 16, 24, 25, 26, 46, 17, 47, 61, 17, 46, 48, 47, 21, 48, 21, 22, 48, 22, 23, 46, 47, 48, 32, 10, 36, 7, 8, 9, 45, 61, 46, 62, 36, 61, 62, 61, 45, 60, 48, 23, 62, 37, 36, 44, 62, 45, 44, 37, 62, 49, 48, 60, 59, 49, 60, 7, 32, 6, 9, 32, 7, 10, 32, 9, 38, 37, 44, 50, 49, 59, 37, 33, 32, 37, 32, 36, 43, 33, 37, 6, 32, 33, 58, 50, 59, 38, 43, 37, 5, 6, 33, 27, 51, 59, 58, 59, 51, 4, 5, 33, 34, 4, 33, 43, 34, 33, 23, 26, 27, 42, 34, 43, 52, 58, 51, 23, 24, 26, 27, 60, 23, 60, 27, 59, 28, 51, 27, 29, 51, 28, 52, 51, 29, 52, 41, 42, 39, 3, 34, 42, 39, 34, 4, 34, 3, 41, 39, 42, 52, 38, 58, 58, 38, 50, 30, 53, 52, 42, 43, 52, 52, 40, 41, 39, 41, 40, 46, 48, 49, 45, 46, 49, 38, 44, 45, 45, 50, 38, 50, 45, 49, 29, 30, 52, 31, 53, 30, 52, 43, 38, 53, 57, 52, 54, 57, 53, 57, 40, 52, 31, 54, 53, 2, 40, 57, 3, 39, 40, 2, 3, 40, 54, 56, 57, 55, 56, 54, 0, 56, 55, 0, 55, 54, 0, 54, 31, 1, 57, 56, 1, 56, 0, 2, 57, 1], "uvs": [0.67748, 0.99322, 0.54948, 1, 0.37215, 0.88122, 0.21748, 0.71588, 0.15615, 0.531, 0.08682, 0.51144, 0.02548, 0.41189, 0.00682, 0.323, 0.00148, 0.22166, 0.06948, 0.227, 0.15082, 0.22878, 0.16415, 0.147, 0.20548, 0.051, 0.23882, 0, 0.26415, 0.06344, 0.33615, 0.10078, 0.39881, 0.13989, 0.43881, 0.211, 0.48415, 0.14344, 0.54415, 0.09544, 0.63481, 0.00655, 0.70948, 0.10255, 0.79748, 0.15944, 0.83779, 0.29768, 0.87215, 0.243, 0.90948, 0.18966, 0.95481, 0.275, 1, 0.37277, 1, 0.53455, 0.92681, 0.68922, 0.82815, 0.80655, 0.76415, 0.83322, 0.15215, 0.31233, 0.16815, 0.43677, 0.18282, 0.53277, 0.23748, 0.18078, 0.28415, 0.27144, 0.32948, 0.36744, 0.34682, 0.41544, 0.24991, 0.72041, 0.32797, 0.7969, 0.30069, 0.69909, 0.29129, 0.58122, 0.3101, 0.49219, 0.39286, 0.36303, 0.45775, 0.32541, 0.51794, 0.30785, 0.57813, 0.25017, 0.6261, 0.30785, 0.68911, 0.3693, 0.73801, 0.4345, 0.78033, 0.52353, 0.77939, 0.64015, 0.75306, 0.78812, 0.71262, 0.88091, 0.67782, 0.95991, 0.5518, 0.96869, 0.40226, 0.87464, 0.75403, 0.46821, 0.77751, 0.4019, 0.80949, 0.34046, 0.41449, 0.274, 0.39756, 0.33419], "vertices": [1, 25, -9.15, -8.83, 1, 1, 25, -9.89, 1.91, 1, 1, 25, -2.85, 17.02, 1, 1, 25, 7.18, 30.31, 1, 1, 25, 18.67, 35.8, 1, 1, 25, 19.73, 41.66, 1, 1, 25, 25.85, 46.99, 1, 1, 25, 31.4, 48.72, 1, 1, 25, 37.77, 49.36, 1, 1, 25, 37.6, 43.64, 1, 1, 25, 37.69, 36.81, 1, 1, 25, 42.87, 35.84, 1, 1, 25, 49.02, 32.55, 1, 1, 25, 52.31, 29.84, 1, 1, 25, 48.38, 27.6, 1, 1, 25, 46.21, 21.48, 1, 1, 25, 43.9, 16.15, 1, 1, 25, 39.52, 12.66, 1, 1, 25, 43.88, 8.98, 1, 1, 25, 47.06, 4.03, 1, 1, 25, 52.88, -3.42, 1, 1, 25, 47.02, -9.87, 1, 1, 25, 43.65, -17.36, 1, 1, 25, 35.05, -21, 1, 1, 25, 38.57, -23.78, 1, 1, 25, 42.02, -26.82, 1, 1, 25, 36.76, -30.78, 1, 1, 25, 30.72, -34.76, 1, 1, 25, 20.53, -35.06, 1, 1, 25, 10.61, -29.2, 1, 1, 25, 2.98, -21.13, 1, 1, 25, 1.14, -15.81, 1, 1, 25, 32.43, 36.54, 1, 1, 25, 24.63, 34.97, 1, 1, 25, 18.62, 33.56, 1, 1, 25, 40.92, 29.62, 1, 1, 25, 35.33, 25.53, 1, 1, 25, 29.4, 21.55, 1, 1, 25, 26.42, 20.01, 1, 1, 25, 6.97, 27.58, 1, 1, 25, 2.35, 20.88, 1, 1, 25, 8.44, 23.35, 1, 1, 25, 15.84, 24.36, 1, 1, 25, 21.49, 22.95, 1, 1, 25, 29.83, 16.24, 1, 1, 25, 32.36, 10.86, 1, 1, 25, 33.61, 5.84, 1, 1, 25, 37.4, 0.89, 1, 1, 25, 33.88, -3.24, 1, 1, 25, 30.17, -8.65, 1, 1, 25, 26.18, -12.88, 1, 1, 25, 20.68, -16.59, 1, 1, 25, 13.33, -16.73, 1, 1, 25, 3.95, -14.79, 1, 1, 25, -1.99, -11.57, 1, 1, 25, -7.05, -8.8, 1, 1, 25, -7.92, 1.77, 1, 1, 25, -2.36, 14.5, 1, 1, 25, 24.1, -14.28, 1, 1, 25, 28.33, -16.13, 1, 1, 25, 32.28, -18.7, 1, 1, 25, 35.49, 14.58, 1, 1, 25, 31.66, 15.89, 1], "width": 84, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 20, 64, 64, 66, 66, 68, 26, 70, 70, 72, 72, 74, 74, 76, 68, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 76, 76, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 80, 100, 116, 116, 102, 116, 118, 118, 120, 120, 46, 34, 122, 122, 124, 124, 88], "type": "mesh", "hull": 32, "height": 63}}, "lotus2": {"lotus2": {"triangles": [3, 4, 6, 4, 5, 6, 3, 6, 2, 6, 7, 2, 7, 8, 2, 1, 2, 8, 1, 8, 0], "uvs": [0.91153, 0, 1, 0.1423, 0.80371, 0.6498, 0.53602, 0.99557, 0.19025, 1, 0, 0.83384, 0.0973, 0.47134, 0.31666, 0.16461, 0.60294, 0.00845], "vertices": [2, 13, -1.68, -6.49, 0.86631, 14, -7.8, -4.31, 0.13369, 2, 13, -4.24, -2.01, 0.99521, 14, -8.86, 0.74, 0.00479, 3, 13, 6.47, 8.85, 0.31121, 14, 4.67, 7.8, 0.66185, 15, -6.46, 6.73, 0.02694, 3, 13, 18.83, 14.95, 0.00222, 14, 18.3, 9.8, 0.15584, 15, 6.58, 11.18, 0.84194, 1, 15, 19.14, 6.26, 1, 1, 15, 24.41, -0.51, 1, 2, 14, 25.46, -10.87, 0.00696, 15, 17.37, -7.84, 0.99304, 2, 14, 13.97, -13.03, 0.3089, 15, 6.46, -12.05, 0.6911, 3, 13, 9.89, -9.25, 0.02083, 14, 2.36, -10.48, 0.92218, 15, -5.41, -11.66, 0.05698], "width": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "type": "mesh", "hull": 9, "height": 26}}, "jackpot5": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-16.09, -14.99, -15.61, 16.01, 16.39, 15.51, 15.9, -15.49], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "10": {"A": {"x": 0.5, "width": 113, "height": 108}, "Q": {"width": 106, "height": 102}, "J": {"x": 0.5, "width": 105, "y": 0.5, "height": 101}, "K": {"x": 0.5, "width": 111, "y": 0.5, "height": 107}, "10": {"x": 0.5, "width": 133, "y": 0.5, "height": 101}}, "jackpot4": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.41, -6.94, -23.41, 7.06, 25.59, 7.06, 25.59, -6.94], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "lotus0": {"lotus0": {"triangles": [8, 9, 10, 5, 2, 3, 7, 22, 6, 4, 5, 3, 0, 2, 5, 22, 0, 5, 6, 22, 5, 7, 20, 22, 1, 2, 0, 21, 22, 20, 18, 19, 20, 11, 12, 20, 7, 10, 11, 10, 7, 8, 13, 18, 12, 11, 20, 7, 20, 12, 18, 14, 18, 13, 17, 18, 14, 15, 17, 14, 16, 17, 15], "uvs": [0.23349, 0.4837, 0.05293, 0.49192, 0, 0.39597, 0, 0.21504, 0.17151, 0, 0.31434, 0.04234, 0.4922, 0.06427, 0.61078, 0.14925, 0.70779, 0.07797, 0.7563, 0.0067, 0.82637, 0.09442, 0.94225, 0.20956, 0.99884, 0.44806, 0.99076, 0.61802, 0.918, 0.78525, 0.74822, 1, 0.63234, 1, 0.66468, 0.87571, 0.76978, 0.63173, 0.58652, 0.60432, 0.53262, 0.48096, 0.47064, 0.53852, 0.34937, 0.4837], "vertices": [1, 26, 5.39, 23.34, 1, 1, 26, 4.09, 33.92, 1, 1, 26, 9.4, 37.47, 1, 1, 26, 19.86, 38.28, 1, 1, 26, 33.08, 29.17, 1, 1, 26, 31.29, 20.57, 1, 1, 26, 30.83, 10.01, 1, 1, 26, 26.46, 2.65, 1, 1, 26, 31.03, -2.73, 1, 1, 26, 35.37, -5.26, 1, 1, 26, 30.62, -9.78, 1, 1, 26, 24.49, -17.12, 1, 1, 26, 10.96, -21.52, 1, 1, 26, 1.1, -21.81, 1, 1, 26, -8.91, -18.28, 1, 1, 26, -22.1, -9.26, 1, 1, 26, -22.63, -2.45, 1, 1, 26, -15.3, -3.79, 1, 1, 26, -0.71, -8.87, 1, 1, 26, 0.04, 2.03, 1, 1, 26, 6.92, 5.76, 1, 1, 26, 3.31, 9.14, 1, 1, 26, 5.92, 16.52, 1], "width": 59, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "type": "mesh", "hull": 23, "height": 58}}}}, "skeleton": {"images": "", "width": 205.5, "spine": "3.7.93", "audio": "", "hash": "T5rmJK7pohKWjtXorgjeOBAaZiI", "height": 133.62}, "slots": [{"attachment": "binhtiendon4", "name": "binhtiendon4", "bone": "bone"}, {"attachment": "A", "name": "10", "bone": "bone29"}, {"attachment": "A", "blend": "screen", "name": "11", "bone": "bone30"}, {"attachment": "vongkimco", "name": "vongkimco", "bone": "bone3"}, {"attachment": "vongkimco", "blend": "additive", "name": "vongkimco2", "bone": "bone4"}, {"attachment": "jackpot4", "name": "jackpot4", "bone": "bone5"}, {"attachment": "jackpot4", "name": "jackpot6", "bone": "bone7"}, {"attachment": "jackpot4", "name": "jackpot10", "bone": "bone11"}, {"attachment": "jackpot4", "name": "jackpot7", "bone": "bone8"}, {"attachment": "jackpot4", "name": "jackpot8", "bone": "bone9"}, {"attachment": "jackpot4", "name": "jackpot9", "bone": "bone10"}, {"attachment": "lotus0", "name": "lotus0", "bone": "root"}, {"attachment": "lotus1", "name": "lotus1", "bone": "root"}, {"attachment": "lotus2", "name": "lotus2", "bone": "root"}, {"attachment": "lotus3", "name": "lotus3", "bone": "root"}, {"attachment": "lotus4", "name": "lotus4", "bone": "root"}, {"attachment": "jackpot5", "name": "jackpot5", "bone": "bone6"}, {"attachment": "jackpot5", "name": "jackpot11", "bone": "bone27"}, {"attachment": "jackpot5", "name": "jackpot12", "bone": "bone28"}, {"attachment": "quadao0", "name": "quadao0", "bone": "root"}, {"attachment": "quadao1", "name": "quadao1", "bone": "root"}], "bones": [{"name": "root"}, {"scaleX": 0.659, "parent": "root", "scaleY": 0.659, "rotation": 147.5, "name": "bone2", "length": 23.68, "shearY": -23.94}, {"parent": "bone2", "name": "bone", "length": 26.8}, {"parent": "root", "rotation": 90, "name": "bone3", "length": 17.25, "x": -0.29, "y": 33.71}, {"parent": "root", "rotation": 90, "name": "bone4", "length": 17.25, "x": -0.29, "y": 33.71}, {"parent": "root", "rotation": 90, "name": "bone5", "length": 7.99, "x": 44.31, "y": -8.28}, {"parent": "root", "rotation": 90.9, "name": "bone6", "length": 4.22, "x": -20.19, "y": 21.18}, {"parent": "root", "rotation": 90, "name": "bone7", "length": 7.99, "x": -47.08, "y": -8.28}, {"parent": "root", "rotation": 90, "name": "bone8", "length": 7.99, "x": -20.81, "y": 22.54}, {"parent": "root", "rotation": 90, "name": "bone9", "length": 7.99, "x": 16.48, "y": 16.53}, {"parent": "root", "rotation": 90, "name": "bone10", "length": 7.99, "x": -10.45, "y": -9.18}, {"parent": "root", "rotation": 90, "name": "bone11", "length": 7.99, "x": 34.14, "y": 21.8}, {"parent": "root", "rotation": 90, "name": "bone12", "length": 11.22, "x": 0.79, "y": -26.97}, {"parent": "bone12", "rotation": 104.47, "name": "bone13", "length": 4.42, "x": 1.15, "y": 6.56}, {"parent": "bone13", "rotation": 17.88, "name": "bone14", "length": 9.8, "x": 4.42}, {"parent": "bone14", "rotation": -10.46, "name": "bone15", "length": 16.66, "x": 9.8}, {"parent": "bone12", "rotation": -137.29, "name": "bone16", "length": 2.44, "x": 3.08, "y": -6.55}, {"parent": "bone16", "rotation": 7.28, "name": "bone17", "length": 10.09, "x": 2.64, "y": 0.01}, {"parent": "bone17", "rotation": 7.13, "name": "bone18", "length": 16.27, "x": 10.09}, {"parent": "bone12", "rotation": -73.93, "name": "bone19", "length": 16.95, "x": 5.56, "y": -6.42}, {"parent": "bone19", "rotation": 15.11, "name": "bone20", "length": 18.39, "x": 16.95}, {"parent": "bone20", "rotation": 32.02, "name": "bone21", "length": 15.61, "x": 18.39}, {"parent": "bone12", "rotation": 76.94, "name": "bone22", "length": 17.71, "x": 3.35, "y": 2.28}, {"parent": "bone22", "rotation": -12.24, "name": "bone23", "length": 16.79, "x": 18.14, "y": -0.04}, {"parent": "bone23", "rotation": -20.87, "name": "bone24", "length": 19.13, "x": 16.97, "y": -0.07}, {"parent": "bone12", "rotation": -1.68, "name": "bone25", "length": 14.08, "x": 13.84, "y": 2.42}, {"parent": "bone12", "rotation": -4.46, "name": "bone26", "length": 10.66, "x": 36.61, "y": -11.66}, {"parent": "root", "rotation": 90.9, "name": "bone27", "length": 4.22, "x": 13.88, "y": 21.91}, {"parent": "root", "rotation": 90.9, "name": "bone28", "length": 4.22, "x": 2.02, "y": 29.23}, {"parent": "root", "name": "bone29", "length": 13}, {"parent": "root", "name": "bone30", "length": 13}, {"parent": "root", "rotation": 89.34, "name": "bone32", "length": 9.22, "x": 2.37, "y": -34.92}, {"parent": "bone32", "rotation": -10.29, "name": "bone31", "length": 31.82, "x": 2.63, "y": 4.18}, {"parent": "bone32", "rotation": 1.24, "name": "bone33", "length": 10.39, "x": 4.28, "y": -3.13}], "animations": {"quadao": {"slots": {"11": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "10": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone31": {"rotate": [{"angle": 0, "time": 0}, {"angle": 14.69, "time": 0.8333}, {"angle": 0, "time": 1.6667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.872, "y": 1, "time": 0.1667}, {"curve": "stepped", "x": 1.014, "y": 1, "time": 0.2667}, {"x": 1.014, "y": 1, "time": 0.6}, {"x": 0.934, "y": 1, "time": 0.7}, {"x": 1, "y": 1, "time": 0.8333}, {"x": 0.872, "y": 1, "time": 1}, {"curve": "stepped", "x": 1.014, "y": 1, "time": 1.1}, {"x": 1.014, "y": 1, "time": 1.4333}, {"x": 0.934, "y": 1, "time": 1.5333}, {"x": 1, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -0.84, "y": -0.01, "time": 0.1667}, {"x": 5.87, "y": 0.07, "time": 0.2667}, {"x": 9.51, "y": 0.11, "time": 0.4333}, {"x": 5.87, "y": 0.07, "time": 0.6}, {"x": -0.84, "y": -0.01, "time": 0.7}, {"x": 0, "y": 0, "time": 0.8333}, {"x": -0.84, "y": -0.01, "time": 1}, {"x": 5.87, "y": 0.07, "time": 1.1}, {"x": 9.51, "y": 0.11, "time": 1.2667}, {"x": 5.87, "y": 0.07, "time": 1.4333}, {"x": -0.84, "y": -0.01, "time": 1.5333}, {"x": 0, "y": 0, "time": 1.6667}]}}, "deform": {"default": {"quadao0": {"quadao0": [{"time": 0}, {"vertices": [1.95551, -0.93672, 0.67718, -0.27863, -0.40798, 0.00429, -0.54885, -0.46962, -1.49318, 0.28767, -2.31051, 0.15991, -1.69646, 0.35759, 0, 0, 0, 0, 0, 0, -1.29472, -0.25848, -1.97401, -0.18358, -1.30164, -0.9384, -1.42793, 0.01485, -0.81526, 0.07658, 0, 0, 2.17657, 0.04619, 2.65461, 0.24524, 2.18212, 0.5901, 1.29123, -0.08092, 1.15591, -0.01131, 0, 0, 0, 0, 0, 0, 0.53148, -1.22926, -0.55577, -1.15018, -1.23991, -1.55106, -0.69106, -1.08075, -2.11836, -1.9781, 0.39029, -0.26591, 0, 0, 1.56252, -0.93272, 1.55984, -1.1946, 1.69085, -1.19615, 1.69754, -0.54114, 1.69486, -0.80299, -0.07772, -0.9511, -0.55855, -1.42206, -0.48361, -0.74294], "time": 0.2667}, {"time": 0.5667}, {"vertices": [1.95551, -0.93672, 0.67718, -0.27863, -0.40798, 0.00429, -0.54885, -0.46962, -1.49318, 0.28767, -2.31051, 0.15991, -1.69646, 0.35759, 0, 0, 0, 0, 0, 0, -1.29472, -0.25848, -1.97401, -0.18358, -1.30164, -0.9384, -1.42793, 0.01485, -0.81526, 0.07658, 0, 0, 2.17657, 0.04619, 2.65461, 0.24524, 2.18212, 0.5901, 1.29123, -0.08092, 1.15591, -0.01131, 0, 0, 0, 0, 0, 0, 0.53148, -1.22926, -0.55577, -1.15018, -1.23991, -1.55106, -0.69106, -1.08075, -2.11836, -1.9781, 0.39029, -0.26591, 0, 0, 1.56252, -0.93272, 1.55984, -1.1946, 1.69085, -1.19615, 1.69754, -0.54114, 1.69486, -0.80299, -0.07772, -0.9511, -0.55855, -1.42206, -0.48361, -0.74294], "time": 0.8333}, {"time": 1.1}, {"vertices": [1.95551, -0.93672, 0.67718, -0.27863, -0.40798, 0.00429, -0.54885, -0.46962, -1.49318, 0.28767, -2.31051, 0.15991, -1.69646, 0.35759, 0, 0, 0, 0, 0, 0, -1.29472, -0.25848, -1.97401, -0.18358, -1.30164, -0.9384, -1.42793, 0.01485, -0.81526, 0.07658, 0, 0, 2.17657, 0.04619, 2.65461, 0.24524, 2.18212, 0.5901, 1.29123, -0.08092, 1.15591, -0.01131, 0, 0, 0, 0, 0, 0, 0.53148, -1.22926, -0.55577, -1.15018, -1.23991, -1.55106, -0.69106, -1.08075, -2.11836, -1.9781, 0.39029, -0.26591, 0, 0, 1.56252, -0.93272, 1.55984, -1.1946, 1.69085, -1.19615, 1.69754, -0.54114, 1.69486, -0.80299, -0.07772, -0.9511, -0.55855, -1.42206, -0.48361, -0.74294], "time": 1.3667}, {"time": 1.6667}]}}}}, "itemAtayduky": {"slots": {"11": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone30": {"scale": [{"x": 1, "y": 1, "time": 0.4667}, {"x": 1.083, "y": 1.083, "time": 0.8}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8667}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.083, "y": 1.083, "time": 1.6667}]}}}, "item10tayduky": {"slots": {"11": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "10", "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "10": {"attachment": [{"name": "10", "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone30": {"scale": [{"x": 1, "y": 1, "time": 0.4667}, {"x": 1.083, "y": 1.083, "time": 0.8}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8667}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.083, "y": 1.083, "time": 1.6667}]}}}, "itemJtayduky": {"slots": {"11": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "J", "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "10": {"attachment": [{"name": "J", "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone30": {"scale": [{"x": 1, "y": 1, "time": 0.4667}, {"x": 1.083, "y": 1.083, "time": 0.8}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8667}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.083, "y": 1.083, "time": 1.6667}]}}}, "itemQtayduky": {"slots": {"11": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "Q", "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "10": {"attachment": [{"name": "Q", "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone30": {"scale": [{"x": 1, "y": 1, "time": 0.4667}, {"x": 1.083, "y": 1.083, "time": 0.8}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8667}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.083, "y": 1.083, "time": 1.6667}]}}}, "itemKtayduky": {"slots": {"11": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "K", "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "10": {"attachment": [{"name": "K", "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone30": {"scale": [{"x": 1, "y": 1, "time": 0.4667}, {"x": 1.083, "y": 1.083, "time": 0.8}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8667}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.083, "y": 1.083, "time": 1.6667}]}}}, "lotus": {"slots": {"11": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "binhtiendon4": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"color": [{"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.3333}]}, "jackpot11": {"color": [{"color": "ffffffff", "time": 1.1667}, {"color": "ffffff00", "time": 1.5}]}, "vongkimco2": {"attachment": [{"name": null, "time": 0}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "vongkimco": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"color": [{"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "10": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone17": {"rotate": [{"angle": 0, "time": 0}, {"angle": -14.77, "time": 0.3333}, {"angle": -9.34, "time": 0.6667}, {"angle": 6.51, "time": 1}, {"angle": 5.11, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone28": {"scale": [{"x": 0, "y": 0, "time": 0}, {"x": 1, "y": 1, "time": 0.3333}, {"x": 0.805, "y": 0.805, "time": 0.6667}, {"x": 0.661, "y": 0.661, "time": 1}, {"x": 0.864, "y": 0.864, "time": 1.3333}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 5.47, "y": 3.46, "time": 0.1667}, {"x": 6.54, "y": 6.86, "time": 0.3333}, {"x": 4.92, "y": 10.21, "time": 0.5}, {"x": -0.21, "y": 13.5, "time": 0.6667}, {"x": -5.85, "y": 16.3, "time": 0.8333}, {"x": -8.44, "y": 19.15, "time": 1}, {"x": -7.71, "y": 22.06, "time": 1.1667}, {"x": -5.89, "y": 24.97, "time": 1.3333}]}, "bone18": {"rotate": [{"angle": 0, "time": 0}, {"angle": 5.3, "time": 0.3333}, {"angle": -1.29, "time": 0.6667}, {"angle": -10.93, "time": 1}, {"angle": 7.84, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone15": {"rotate": [{"angle": 0, "time": 0}, {"angle": -16.62, "time": 0.3333}, {"angle": 4.48, "time": 0.6667}, {"angle": 7.73, "time": 1}, {"angle": -20.44, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone26": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": -0.77, "y": 0.06, "time": 0.3}, {"x": -0.77, "y": 0.06, "time": 0.6667}, {"x": 0.32, "y": -0.02, "time": 1.1667}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone27": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.1667}, {"x": 1, "y": 1, "time": 0.5}, {"x": 0.805, "y": 0.805, "time": 0.8333}, {"x": 0.661, "y": 0.661, "time": 1.1667}, {"x": 0.864, "y": 0.864, "time": 1.5}], "translate": [{"x": 0, "y": 0, "time": 0.1667}, {"x": 5.47, "y": 3.46, "time": 0.3333}, {"x": 6.54, "y": 6.86, "time": 0.5}, {"x": 4.92, "y": 10.21, "time": 0.6667}, {"x": -0.21, "y": 13.5, "time": 0.8333}, {"x": -5.85, "y": 16.3, "time": 1}, {"x": -8.44, "y": 19.15, "time": 1.1667}, {"x": -7.71, "y": 22.06, "time": 1.3333}, {"x": -5.89, "y": 24.97, "time": 1.5}]}, "bone20": {"rotate": [{"angle": 0, "time": 0}, {"angle": 5.01, "time": 0.8333}, {"angle": 0, "time": 1.6667}]}, "bone6": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 1, "y": 1, "time": 0.6667}, {"x": 0.805, "y": 0.805, "time": 1}, {"x": 0.661, "y": 0.661, "time": 1.3333}, {"x": 0.864, "y": 0.864, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 5.47, "y": 3.46, "time": 0.5}, {"x": 6.54, "y": 6.86, "time": 0.6667}, {"x": 4.92, "y": 10.21, "time": 0.8333}, {"x": -0.21, "y": 13.5, "time": 1}, {"x": -5.85, "y": 16.3, "time": 1.1667}, {"x": -8.44, "y": 19.15, "time": 1.3333}, {"x": -7.71, "y": 22.06, "time": 1.5}, {"x": -5.89, "y": 24.97, "time": 1.6667}]}, "bone24": {"rotate": [{"angle": 0, "time": 0}, {"angle": 7, "time": 0.8333}, {"angle": 0, "time": 1.6667}]}, "bone14": {"rotate": [{"angle": 0, "time": 0}, {"angle": 8.65, "time": 0.3333}, {"angle": 2.75, "time": 0.6667}, {"angle": -7.45, "time": 1}, {"angle": 3.31, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone12": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 7.42, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone23": {"rotate": [{"angle": 0, "time": 0}, {"angle": -5.91, "time": 0.8333}, {"angle": 0, "time": 1.6667}]}}, "deform": {"default": {"lotus1": {"lotus1": [{"time": 0}, {"offset": 10, "vertices": [2.4662, -0.14156, 1.72384, -0.37749, 1.18906, -0.39321, 2.15478, -0.47185, 1.39039, 0.04089, 0, 0, 1.62002, -0.48758, 1.39982, -0.27997, 1.37781, 0.4687, 0.64801, -0.19503, 0, 0, 0, 0, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.27367, -2.02583, -1.00662, -2.17052, 0.16986, -2.13591, 0.24536, -1.06324, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45298, 0.84303, -0.45298, 0.84303, -0.45298, 0.84303, 0.40579, 0.76124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.17648, 0.0346, 1.17648, 0.0346, 1.17648, 0.0346, 1.82134, -0.05347, 1.60429, 0.04719, 2.3561, -0.03774, 2.57945, -0.35232, 0, 0, 0.09752, 0.32401, 0.37819, 0.81548, 0, 0, 0, 0, 0, 0, 0, 0, 0.80844, -2.0101, -0.36804, -2.0447, -0.36804, -2.0447], "time": 0.3}, {"time": 0.5667}, {"offset": 10, "vertices": [2.4662, -0.14156, 1.72384, -0.37749, 1.18906, -0.39321, 2.15478, -0.47185, 1.39039, 0.04089, 0, 0, 1.62002, -0.48758, 1.39982, -0.27997, 1.37781, 0.4687, 0.64801, -0.19503, 0, 0, 0, 0, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.27367, -2.02583, -1.00662, -2.17052, 0.16986, -2.13591, 0.24536, -1.06324, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45298, 0.84303, -0.45298, 0.84303, -0.45298, 0.84303, 0.40579, 0.76124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.17648, 0.0346, 1.17648, 0.0346, 1.17648, 0.0346, 1.82134, -0.05347, 1.60429, 0.04719, 2.3561, -0.03774, 2.57945, -0.35232, 0, 0, 0.09752, 0.32401, 0.37819, 0.81548, 0, 0, 0, 0, 0, 0, 0, 0, 0.80844, -2.0101, -0.36804, -2.0447, -0.36804, -2.0447], "time": 0.8333}, {"time": 1.1}, {"offset": 10, "vertices": [2.4662, -0.14156, 1.72384, -0.37749, 1.18906, -0.39321, 2.15478, -0.47185, 1.39039, 0.04089, 0, 0, 1.62002, -0.48758, 1.39982, -0.27997, 1.37781, 0.4687, 0.64801, -0.19503, 0, 0, 0, 0, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.64171, 0.01887, 0.27367, -2.02583, -1.00662, -2.17052, 0.16986, -2.13591, 0.24536, -1.06324, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45298, 0.84303, -0.45298, 0.84303, -0.45298, 0.84303, 0.40579, 0.76124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.17648, 0.0346, 1.17648, 0.0346, 1.17648, 0.0346, 1.82134, -0.05347, 1.60429, 0.04719, 2.3561, -0.03774, 2.57945, -0.35232, 0, 0, 0.09752, 0.32401, 0.37819, 0.81548, 0, 0, 0, 0, 0, 0, 0, 0, 0.80844, -2.0101, -0.36804, -2.0447, -0.36804, -2.0447], "time": 1.3667}, {"time": 1.6667}]}, "lotus0": {"lotus0": [{"time": 0}, {"offset": 4, "vertices": [0.15452, -1.98299, 0.18934, -0.92407, 2.19204, -2.52833, 1.10434, -0.61807, 0.5393, -0.8968, -1.39824, -1.63456, -0.15149, -1.06799, 0.94378, -1.56942, 1.29371, -1.54214, 0.66503, -1.00436, 0.56657, -1.24675], "time": 0.2667}, {"time": 0.5667}, {"offset": 4, "vertices": [0.15452, -1.98299, 0.18934, -0.92407, 2.19204, -2.52833, 1.10434, -0.61807, 0.5393, -0.8968, -1.39824, -1.63456, -0.15149, -1.06799, 0.94378, -1.56942, 1.29371, -1.54214, 0.66503, -1.00436, 0.56657, -1.24675], "time": 0.8}, {"time": 1.1}, {"offset": 4, "vertices": [0.15452, -1.98299, 0.18934, -0.92407, 2.19204, -2.52833, 1.10434, -0.61807, 0.5393, -0.8968, -1.39824, -1.63456, -0.15149, -1.06799, 0.94378, -1.56942, 1.29371, -1.54214, 0.66503, -1.00436, 0.56657, -1.24675], "time": 1.3667}, {"time": 1.6667}]}}}}, "vongkimco": {"slots": {"11": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffff3c", "time": 0.7667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.5}]}, "jackpot6": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff3c", "time": 0.4333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.1667}]}, "jackpot9": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff3c", "time": 0.4333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.1667}]}, "jackpot8": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffff3c", "time": 0.9333}, {"color": "ffffffff", "time": 1.5}, {"color": "ffffff00", "time": 1.6667}]}, "binhtiendon4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.8333}, {"color": "ffffff00", "time": 1.6667}]}, "jackpot10": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffff3c", "time": 0.7667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.5}]}, "quadao1": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "vongkimco2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff91", "time": 0.8333}, {"color": "ffffff00", "time": 1.6667}]}, "quadao0": {"attachment": [{"name": null, "time": 0}]}, "lotus3": {"attachment": [{"name": null, "time": 0}]}, "lotus4": {"attachment": [{"name": null, "time": 0}]}, "lotus1": {"attachment": [{"name": null, "time": 0}]}, "lotus2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "10": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff3c", "time": 0.1}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 0.8667}, {"color": "ffffff3c", "time": 0.9667}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "time": 1.6667}]}, "lotus0": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone3": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 17.25, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone2": {"scale": [{"x": 0.543, "y": 0.543, "time": 0}, {"x": 0.88, "y": 0.88, "time": 0.5667}, {"x": 0.995, "y": 0.995, "time": 0.8333}, {"x": 0.88, "y": 0.88, "time": 1.1}, {"x": 0.543, "y": 0.543, "time": 1.6667}]}, "bone5": {"scale": [{"x": 0.126, "y": 1, "time": 0}, {"x": 1.3, "y": 1, "time": 0.8}, {"x": 0.126, "y": 1, "time": 0.8667}, {"x": 1.3, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": -10.4, "time": 0}, {"x": 0, "y": 52.74, "time": 0.8}, {"x": 0, "y": -16.51, "time": 0.8667}, {"x": 0, "y": 39.68, "time": 1.6667}]}, "bone4": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 17.25, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone10": {"scale": [{"curve": "stepped", "x": 0.126, "y": 1, "time": 0}, {"x": 0.126, "y": 1, "time": 0.3333}, {"x": 1.128, "y": 1, "time": 1.1667}], "translate": [{"curve": "stepped", "x": 0, "y": -10.4, "time": 0}, {"x": 0, "y": -10.4, "time": 0.3333}, {"x": 0, "y": 40.31, "time": 1.1667}]}, "bone": {"rotate": [{"angle": 0, "time": 0}, {"angle": -179.99, "time": 0.8333}, {"angle": 90, "time": 1.2667}, {"angle": 0, "time": 1.6667}]}, "bone7": {"scale": [{"curve": "stepped", "x": 0.126, "y": 1, "time": 0}, {"x": 0.126, "y": 1, "time": 0.3333}, {"x": 1.3, "y": 1, "time": 1.1667}], "translate": [{"curve": "stepped", "x": 0, "y": -10.4, "time": 0}, {"x": 0, "y": -10.4, "time": 0.3333}, {"x": 0, "y": 43.72, "time": 1.1667}]}, "bone9": {"scale": [{"curve": "stepped", "x": 0.126, "y": 1, "time": 0}, {"x": 0.126, "y": 1, "time": 0.8333}, {"x": 1.128, "y": 1, "time": 1.6667}], "translate": [{"curve": "stepped", "x": 0, "y": -10.4, "time": 0}, {"x": 0, "y": -10.4, "time": 0.8333}, {"x": 0, "y": 40.31, "time": 1.6667}]}, "bone8": {"scale": [{"curve": "stepped", "x": 0.126, "y": 1, "time": 0}, {"x": 0.126, "y": 1, "time": 0.6667}, {"x": 1.128, "y": 1, "time": 1.5}], "translate": [{"curve": "stepped", "x": 0, "y": -10.4, "time": 0}, {"x": 0, "y": -10.4, "time": 0.6667}, {"x": 0, "y": 24.89, "time": 1.5}]}, "bone11": {"scale": [{"curve": "stepped", "x": 0.126, "y": 1, "time": 0}, {"x": 0.126, "y": 1, "time": 0.6667}, {"x": 1.3, "y": 1, "time": 1.5}], "translate": [{"curve": "stepped", "x": 0, "y": -10.4, "time": 0}, {"x": 0, "y": -10.4, "time": 0.6667}, {"x": 0, "y": 24.54, "time": 1.5}]}}}}}