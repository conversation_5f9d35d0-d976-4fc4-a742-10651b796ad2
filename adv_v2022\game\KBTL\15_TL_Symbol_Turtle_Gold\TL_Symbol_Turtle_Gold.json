{"skeleton": {"hash": "d1T/fQw/em+aNNar11rMKfnsZRk", "spine": "3.7.94", "width": 123.94, "height": 122, "images": "./images/", "audio": "D:/Work/06_2020/TuLinh/Rùa"}, "bones": [{"name": "root"}, {"name": "Scale", "parent": "root"}, {"name": "All", "parent": "Scale", "y": 5.12}, {"name": "Body", "parent": "All", "x": -15.42, "y": 4.99, "color": "fd0000ff"}, {"name": "3D_L", "parent": "Body", "x": -42.7, "y": -4.4, "color": "fd0000ff"}, {"name": "3D_R", "parent": "Body", "x": 52.12, "y": 4.4, "color": "fd0000ff"}, {"name": "3D_T", "parent": "Body", "x": 5.34, "y": 36.42, "color": "fd0000ff"}, {"name": "Neck", "parent": "Body", "length": 15.68, "rotation": -1.01, "x": 18.71, "y": -14.13, "color": "00500fff"}, {"name": "Neck2", "parent": "Neck", "length": 15.44, "rotation": 5.09, "x": 15.68, "color": "00500fff"}, {"name": "Head", "parent": "Neck2", "x": 10.11, "y": -0.94, "color": "2ff30aff"}, {"name": "Head_Turtle_B", "parent": "Head", "length": 26.84, "rotation": -4.95, "x": 2.59, "y": -4.68, "color": "19cc3eff"}, {"name": "3D_Head_R", "parent": "Head", "x": 26.46, "y": 0.25, "color": "28ff00ff"}, {"name": "3D_Head_T", "parent": "Head", "rotation": 85.91, "x": 11.06, "y": 14.83, "color": "28ff00ff"}, {"name": "Leg1", "parent": "Body", "length": 17.52, "rotation": -48.65, "x": 11.83, "y": -16.34, "color": "ff00d7ff"}, {"name": "Leg2", "parent": "Leg1", "length": 12.89, "rotation": -42.52, "x": 17.52, "color": "ff00d7ff"}, {"name": "Leg3", "parent": "Leg2", "length": 15.41, "rotation": 37.84, "x": 12.89, "color": "ff00d7ff"}, {"name": "Leg4", "parent": "Body", "length": 13.84, "rotation": -81.25, "x": -27.52, "y": -5.78, "color": "ff00d7ff"}, {"name": "Leg5", "parent": "Leg4", "length": 10.5, "rotation": -30.81, "x": 13.84, "color": "ff00d7ff"}, {"name": "Leg6", "parent": "Leg5", "length": 15.1, "rotation": 57.14, "x": 10.5, "color": "ff00d7ff"}, {"name": "Leg7", "parent": "Body", "length": 12.32, "rotation": -50.19, "x": 42.44, "y": -4.99, "color": "ff00d7ff"}, {"name": "Leg8", "parent": "Leg7", "length": 11.34, "rotation": -43.8, "x": 12.32, "color": "ff00d7ff"}, {"name": "Leg9", "parent": "Leg8", "length": 13.8, "rotation": 63.03, "x": 11.34, "color": "ff00d7ff"}, {"name": "Tail_L", "parent": "Body", "length": 17.73, "rotation": 138.85, "x": -30.91, "y": 0.73, "color": "ff0ef6ff"}, {"name": "3D", "parent": "Body", "x": 15.42, "y": 75.01, "color": "e6ff00ff"}, {"name": "3D_Head", "parent": "Head", "rotation": -4.09, "x": 13.21, "y": 3.68, "color": "2ff30aff"}, {"name": "3D_Head_L", "parent": "Head", "x": -2.08, "y": 4.15, "color": "2ff30aff"}, {"name": "Leg10", "parent": "Body", "length": 12.32, "rotation": -63.4, "x": -7.42, "y": 1.73, "color": "ff00d7ff"}, {"name": "Leg11", "parent": "Leg10", "length": 11.34, "rotation": -43.8, "x": 12.32, "color": "ff00d7ff"}, {"name": "Leg12", "parent": "Leg11", "length": 13.8, "rotation": 63.03, "x": 11.34, "color": "ff00d7ff"}, {"name": "Translate3", "parent": "All", "x": -36.35, "y": -42.44, "color": "fd0000ff"}, {"name": "IK_Toe3", "parent": "Translate3", "x": 0.77, "y": 5.78, "color": "ff3f00ff"}, {"name": "IK_Leg3", "parent": "IK_Toe3", "x": -8.89, "y": 12.31, "color": "ff3f00ff"}, {"name": "Translate1", "parent": "All", "x": 17.48, "y": -54.07, "color": "fd0000ff"}, {"name": "IK_Toe1", "parent": "Translate1", "x": -1.83, "y": 5.32, "color": "ff3f00ff"}, {"name": "IK_Leg1", "parent": "IK_Toe1", "x": -9.8, "y": 12.31, "color": "ff3f00ff"}, {"name": "Translate2", "parent": "All", "x": 42.54, "y": -35.47, "color": "fd0000ff"}, {"name": "IK_Toe2", "parent": "Translate2", "x": 2.58, "y": 6.52, "color": "ff3f00ff"}, {"name": "IK_Leg2", "parent": "IK_Toe2", "x": -11.17, "y": 7.07, "color": "ff3f00ff"}, {"name": "Translate4", "parent": "All", "x": -14.34, "y": -28.65, "color": "fd0000ff"}, {"name": "IK_Toe4", "parent": "Translate4", "x": 2.24, "y": 3.84, "color": "ff3f00ff"}, {"name": "IK_Leg4", "parent": "IK_Toe4", "x": -8.89, "y": 10.03, "color": "ff3f00ff"}, {"name": "Fx/Comp 1_2", "parent": "All", "y": -5.12, "scaleX": 1.1, "scaleY": 1.1}, {"name": "Fx/Comp 1_3", "parent": "All", "y": -5.12, "scaleX": 1.1, "scaleY": 1.1}, {"name": "Fx/Comp 1_4", "parent": "All", "y": -5.12, "scaleX": 1.1, "scaleY": 1.1}, {"name": "Sparkle", "parent": "All", "x": 17.92, "y": 6.14}, {"name": "Sparkle2", "parent": "All", "rotation": 16.93, "x": 17.92, "y": 6.14}, {"name": "Sparkle3", "parent": "All", "rotation": 22.87, "x": -24.43, "y": 28.67, "scaleX": 0.886, "scaleY": 0.886}], "slots": [{"name": "BG", "bone": "Scale", "attachment": "BG"}, {"name": "Fx/Comp 1_2", "bone": "Fx/Comp 1_2", "color": "ffffff93"}, {"name": "Fx/Comp 1_3", "bone": "Fx/Comp 1_3", "color": "ffffff93"}, {"name": "Fx/Comp 1_4", "bone": "Fx/Comp 1_4", "color": "ffffff93"}, {"name": "Leg2", "bone": "Leg7", "attachment": "Leg2"}, {"name": "Leg4", "bone": "Leg10", "attachment": "Leg2"}, {"name": "Body_Back", "bone": "Body", "attachment": "Body_Back"}, {"name": "Neck", "bone": "Neck", "attachment": "Neck"}, {"name": "Head_Turtle_B", "bone": "Head_Turtle_B", "attachment": "Head_Turtle_B"}, {"name": "Head_Turtle", "bone": "Head", "attachment": "Head_Turtle"}, {"name": "Leg1_B2", "bone": "Leg4", "attachment": "Leg1_B"}, {"name": "Leg3", "bone": "Leg4", "attachment": "Leg3"}, {"name": "Leg1_B", "bone": "Leg1", "attachment": "Leg1_B"}, {"name": "Leg1", "bone": "Leg1", "attachment": "Leg1"}, {"name": "Tail_L", "bone": "Tail_L", "attachment": "Tail_L"}, {"name": "Body", "bone": "Body", "attachment": "Body"}, {"name": "Sparkle", "bone": "Sparkle"}, {"name": "Sparkle2", "bone": "Sparkle2"}, {"name": "Sparkle3", "bone": "Sparkle3"}, {"name": "body2", "bone": "Body", "attachment": "body2"}], "ik": [{"name": "IK_Leg1", "order": 4, "bones": ["Leg1", "Leg2"], "target": "IK_Leg1", "bendPositive": false}, {"name": "IK_Leg2", "order": 6, "bones": ["Leg7", "Leg8"], "target": "IK_Leg2", "bendPositive": false}, {"name": "IK_Leg3", "order": 8, "bones": ["Leg4", "Leg5"], "target": "IK_Leg3", "bendPositive": false}, {"name": "IK_Leg4", "order": 10, "bones": ["Leg10", "Leg11"], "target": "IK_Leg4", "bendPositive": false}, {"name": "IK_Toe1", "order": 5, "bones": ["Leg3"], "target": "IK_Toe1"}, {"name": "IK_Toe2", "order": 7, "bones": ["Leg9"], "target": "IK_Toe2"}, {"name": "IK_Toe3", "order": 9, "bones": ["Leg6"], "target": "IK_Toe3"}, {"name": "IK_Toe4", "order": 11, "bones": ["Leg12"], "target": "IK_Toe4"}], "transform": [{"name": "3D_HeadL", "order": 15, "bones": ["3D_Head_L"], "target": "3D_Head", "rotation": 4.09, "x": -15.28, "y": -0.62, "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_HeadR", "order": 14, "bones": ["3D_Head_R"], "target": "3D_Head", "rotation": 4.09, "x": 13.46, "y": -2.48, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_HeadT", "order": 13, "bones": ["3D_Head_T"], "target": "3D_Head", "rotation": 90, "x": -2.94, "y": 10.97, "rotateMix": 0, "translateMix": 0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_L", "order": 2, "bones": ["3D_L"], "target": "3D", "x": -58.12, "y": -79.41, "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Neck", "order": 12, "bones": ["Neck"], "target": "3D_R", "rotation": -1.01, "x": -33.41, "y": -18.53, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_R", "order": 1, "bones": ["3D_R"], "target": "3D", "x": 36.7, "y": -70.61, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_T", "order": 0, "bones": ["3D_T"], "target": "3D", "x": -10.08, "y": -38.59, "rotateMix": 0, "translateMix": 0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Tail", "order": 3, "bones": ["Tail_L"], "target": "3D_L", "rotation": 138.85, "x": 11.79, "y": 5.13, "shearY": -360, "rotateMix": 0, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0}], "skins": {"default": {"BG": {"BG": {"width": 122, "height": 122}}, "Body": {"Body": {"type": "mesh", "uvs": [0.71933, 0.05758, 0.80744, 0.16758, 0.89555, 0.29738, 0.94755, 0.39638, 1, 0.49318, 1, 0.56138, 0.897, 0.62518, 0.82622, 0.77258, 0.73666, 0.91118, 0.59655, 1, 0.41311, 1, 0.234, 0.94858, 0.09678, 0.82538, 0, 0.67358, 0.05489, 0.52838, 0.10544, 0.34358, 0.18344, 0.17198, 0.27589, 0.05318, 0.377, 0, 0.56189, 0, 0.182, 0.62958, 0.33366, 0.68898, 0.533, 0.68238, 0.65722, 0.60758, 0.76988, 0.42938, 0.22244, 0.40738, 0.37844, 0.45138, 0.56911, 0.47778, 0.77422, 0.29298, 0.598, 0.14338, 0.36689, 0.16318, 0.27589, 0.25558, 0.11816, 0.77994, 0.27525, 0.868, 0.42797, 0.89292, 0.58288, 0.87465, 0.69634, 0.81982, 0.77816, 0.71348, 0.85779, 0.58055, 0.94574, 0.53178], "triangles": [10, 34, 9, 34, 35, 9, 9, 35, 8, 10, 33, 34, 35, 36, 8, 8, 36, 7, 33, 21, 34, 34, 22, 35, 34, 21, 22, 35, 22, 36, 22, 23, 36, 36, 37, 7, 36, 23, 37, 7, 37, 6, 37, 38, 6, 37, 23, 38, 22, 21, 26, 21, 20, 26, 22, 27, 23, 22, 26, 27, 20, 25, 26, 6, 39, 5, 6, 38, 39, 23, 24, 38, 23, 27, 24, 38, 24, 39, 39, 4, 5, 39, 3, 4, 39, 24, 3, 28, 24, 27, 31, 30, 26, 26, 30, 29, 27, 26, 29, 30, 19, 29, 24, 2, 3, 24, 28, 2, 28, 27, 29, 28, 1, 2, 28, 29, 1, 1, 29, 0, 29, 19, 0, 11, 33, 10, 11, 12, 33, 12, 32, 33, 33, 32, 21, 21, 32, 20, 12, 13, 32, 32, 13, 20, 13, 14, 20, 20, 14, 25, 14, 15, 25, 25, 31, 26, 19, 30, 18, 25, 15, 31, 15, 16, 31, 31, 16, 30, 16, 17, 30, 30, 17, 18], "vertices": [2, 4, 70.1, 39.27, 0.472, 5, -24.73, 30.48, 0.528, 2, 4, 78.82, 32.12, 0.444, 5, -16.01, 23.33, 0.556, 2, 4, 87.54, 23.69, 0.416, 5, -7.28, 14.9, 0.584, 2, 4, 92.69, 17.25, 0.432, 5, -2.13, 8.46, 0.568, 2, 4, 97.89, 10.96, 0.424, 5, 3.06, 2.17, 0.576, 2, 4, 97.89, 6.53, 0.4, 5, 3.06, -2.26, 0.6, 2, 4, 87.69, 2.38, 0.472, 5, -7.14, -6.41, 0.528, 2, 4, 80.68, -7.2, 0.4, 5, -14.15, -15.99, 0.6, 2, 4, 71.81, -16.21, 0.4, 5, -23.01, -25, 0.6, 2, 4, 57.94, -21.98, 0.424, 5, -36.88, -30.77, 0.576, 2, 4, 39.78, -21.98, 0.496, 5, -55.04, -30.77, 0.504, 2, 4, 22.05, -18.64, 0.552, 5, -72.78, -27.43, 0.448, 2, 4, 8.47, -10.63, 0.608, 5, -86.36, -19.42, 0.392, 3, 4, -1.11, -0.77, 0.57306, 5, -95.94, -9.56, 0.39494, 6, -49.16, -41.58, 0.032, 2, 4, 4.32, 8.67, 0.568, 5, -90.51, -0.12, 0.432, 2, 4, 9.32, 20.68, 0.552, 5, -85.5, 11.89, 0.448, 2, 4, 17.05, 31.84, 0.552, 5, -77.78, 23.05, 0.448, 2, 4, 26.2, 39.56, 0.568, 5, -68.63, 30.77, 0.432, 2, 4, 36.21, 43.02, 0.568, 5, -58.62, 34.23, 0.432, 2, 4, 54.51, 43.02, 0.476, 5, -40.32, 34.23, 0.524, 3, 4, 16.9, 2.09, 0.44928, 5, -77.92, -6.7, 0.48672, 6, -31.14, -38.72, 0.064, 3, 4, 31.92, -1.77, 0.40832, 5, -62.91, -10.56, 0.47168, 6, -16.12, -42.59, 0.12, 3, 4, 51.65, -1.34, 0.35319, 5, -43.18, -10.13, 0.58214, 6, 3.61, -42.16, 0.06467, 3, 4, 63.95, 3.52, 0.2968, 5, -30.88, -5.27, 0.32595, 6, 15.91, -37.29, 0.37725, 3, 4, 75.1, 15.11, 0.32825, 5, -19.72, 6.32, 0.47821, 6, 27.06, -25.71, 0.19354, 3, 4, 20.91, 16.54, 0.47008, 5, -73.92, 7.75, 0.43392, 6, -27.13, -24.28, 0.096, 3, 4, 36.35, 13.68, 0.43802, 5, -58.48, 4.89, 0.48998, 6, -11.69, -27.14, 0.072, 3, 4, 55.23, 11.96, 0.3546, 5, -39.6, 3.17, 0.56448, 6, 7.19, -28.86, 0.08092, 3, 4, 75.53, 23.97, 0.39398, 5, -19.29, 15.18, 0.52296, 6, 27.49, -16.85, 0.08306, 2, 4, 58.09, 33.7, 0.42, 5, -36.74, 24.91, 0.58, 2, 4, 35.21, 32.41, 0.48, 5, -59.62, 23.62, 0.52, 3, 4, 26.2, 26.4, 0.48154, 5, -68.63, 17.61, 0.43046, 6, -21.84, -14.42, 0.088, 3, 4, 10.58, -7.68, 0.54938, 5, -84.24, -16.47, 0.37862, 6, -37.46, -48.5, 0.072, 3, 4, 26.13, -13.4, 0.40909, 5, -68.69, -22.19, 0.34291, 6, -21.91, -54.22, 0.248, 3, 4, 41.25, -15.02, 0.35123, 5, -53.57, -23.81, 0.43277, 6, -6.79, -55.84, 0.216, 3, 4, 56.59, -13.83, 0.23795, 5, -38.24, -22.63, 0.36205, 6, 8.55, -54.65, 0.4, 3, 4, 67.82, -10.27, 0.27577, 5, -27.01, -19.06, 0.54823, 6, 19.78, -51.09, 0.176, 3, 4, 75.92, -3.36, 0.27648, 5, -18.91, -12.15, 0.49152, 6, 27.88, -44.18, 0.232, 3, 4, 83.81, 5.28, 0.33931, 5, -11.02, -3.51, 0.6265, 6, 35.76, -35.54, 0.0342, 3, 4, 92.51, 8.45, 0.34277, 5, -2.31, -0.34, 0.6064, 6, 44.47, -32.37, 0.05083], "hull": 20, "edges": [24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 38, 36, 38, 36, 34, 34, 32, 32, 30, 30, 28, 24, 26, 26, 28], "width": 97, "height": 63}}, "Body_Back": {"Body_Back": {"type": "mesh", "uvs": [1, 0.09458, 0.92711, 0.27011, 0.82114, 0.51695, 0.66008, 0.64859, 0.41, 0.67053, 0.1684, 0.56631, 0, 0.41273, 0, 0.16181, 0.15095, 0.11967, 0.35497, 0.05507, 0.53716, 0, 0.79147, 0, 1, 0, 0.20738, 0.33876, 0.38319, 0.33033, 0.57853, 0.26011, 0.78255, 0.13372], "triangles": [4, 5, 14, 14, 9, 15, 13, 8, 14, 11, 12, 0, 1, 15, 16, 7, 8, 13, 16, 10, 11, 16, 11, 0, 1, 16, 0, 8, 9, 14, 5, 13, 14, 6, 7, 13, 15, 10, 16, 14, 15, 3, 9, 10, 15, 2, 15, 1, 4, 14, 3, 3, 15, 2, 5, 6, 13], "vertices": [2, 4, 93.07, 5.81, 0.452, 5, -1.76, -2.98, 0.548, 2, 4, 86.65, -6.13, 0.452, 5, -8.18, -14.92, 0.548, 2, 4, 77.33, -22.91, 0.668, 5, -17.5, -31.7, 0.332, 2, 4, 63.52, -32.48, 0.636, 5, -31.3, -41.28, 0.364, 2, 4, 41.15, -33.36, 0.62, 5, -53.68, -42.15, 0.38, 2, 4, 19.88, -26.27, 0.596, 5, -74.94, -35.06, 0.404, 2, 4, 5.07, -15.83, 0.652, 5, -89.76, -24.62, 0.348, 2, 4, 5.07, 1.24, 0.452, 5, -89.76, -7.56, 0.548, 2, 4, 18.35, 4.1, 0.5, 5, -76.48, -4.69, 0.5, 2, 4, 36.3, 8.49, 0.5, 5, -58.53, -0.3, 0.5, 2, 4, 52.34, 12.24, 0.5, 5, -42.49, 3.45, 0.5, 2, 4, 74.71, 12.24, 0.468, 5, -20.11, 3.45, 0.532, 2, 4, 93.07, 12.24, 0.452, 5, -1.76, 3.45, 0.548, 2, 4, 23.31, -10.8, 0.356, 5, -71.51, -19.59, 0.644, 2, 4, 38.79, -10.22, 0.244, 5, -56.04, -19.02, 0.756, 2, 4, 55.98, -5.45, 0.292, 5, -38.85, -14.24, 0.708, 2, 4, 73.93, 3.15, 0.388, 5, -20.9, -5.65, 0.612], "hull": 13, "edges": [12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 0, 24, 2, 0, 22, 24, 20, 22, 20, 18, 18, 16, 12, 14, 16, 14], "width": 86, "height": 66}}, "Fx/Comp 1_2": {"Fx/Comp 1_00002": {"x": 0.26, "y": 26.71, "width": 93, "height": 31}, "Fx/Comp 1_00003": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00004": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00005": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00007": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00009": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00011": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00013": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00015": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00017": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00019": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00021": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}}, "Fx/Comp 1_3": {"Fx/Comp 1_00002": {"x": 0.26, "y": 26.71, "width": 93, "height": 31}, "Fx/Comp 1_00003": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00004": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00005": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00007": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00009": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00011": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00013": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00015": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00017": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00019": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00021": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}}, "Fx/Comp 1_4": {"Fx/Comp 1_00002": {"x": 0.26, "y": 26.71, "width": 93, "height": 31}, "Fx/Comp 1_00003": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00004": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00005": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00007": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00009": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00011": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00013": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00015": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00017": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00019": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}, "Fx/Comp 1_00021": {"x": 0.26, "y": 27.21, "width": 93, "height": 31}}, "Head_Turtle": {"Head_Turtle": {"type": "mesh", "uvs": [1, 0.51625, 0.89318, 0.79811, 0.88989, 1, 0.70854, 0.88225, 0.54037, 0.89066, 0.37881, 1, 0.17438, 1, 0, 0.89487, 0, 0.33114, 0.39859, 0.11659, 0.6261, 0.1797, 0.78767, 0, 0.91956, 0, 0.1423, 0.6116, 0.2709, 0.51484, 0.40608, 0.64736, 0.33849, 0.71467, 0.84105, 0.69694, 0.74759, 0.43021, 0.48197, 0.54945, 0.25208, 0.85475, 0.22744, 0.35682, 0.28413, 0.62244], "triangles": [2, 3, 1, 6, 20, 5, 5, 20, 4, 20, 16, 4, 16, 15, 4, 3, 4, 19, 4, 15, 19, 3, 17, 1, 3, 19, 17, 20, 13, 16, 1, 17, 0, 13, 22, 16, 16, 22, 15, 19, 18, 17, 17, 18, 0, 19, 15, 14, 15, 22, 14, 0, 18, 12, 12, 10, 11, 19, 21, 9, 19, 10, 18, 19, 9, 10, 19, 14, 21, 12, 18, 10, 13, 14, 22, 6, 7, 20, 7, 13, 20, 7, 8, 13, 13, 21, 14, 13, 8, 21, 21, 8, 9], "vertices": [2, 25, 35.23, 1.78, 0.156, 11, 6.69, 5.68, 0.844, 2, 25, 30.7, -6.09, 0.452, 11, 2.16, -2.19, 0.548, 2, 25, 30.16, -11.92, 0.292, 11, 1.62, -8.02, 0.708, 2, 25, 23.71, -8.03, 0.292, 11, -4.83, -4.14, 0.708, 2, 25, 17.49, -7.83, 0.244, 11, -11.05, -3.94, 0.756, 2, 25, 11.3, -10.57, 0.22, 11, -17.24, -6.67, 0.78, 2, 25, 3.76, -10.03, 0.548, 11, -24.78, -6.13, 0.452, 2, 25, -2.46, -6.53, 0.676, 11, -31, -2.63, 0.324, 2, 25, -1.3, 9.77, 0.684, 11, -29.84, 13.67, 0.316, 2, 25, 13.86, 14.93, 0.244, 11, -14.68, 18.83, 0.756, 2, 25, 22.12, 12.5, 0.1, 11, -6.42, 16.4, 0.9, 2, 25, 28.46, 17.28, 0.156, 11, -0.08, 21.18, 0.844, 2, 25, 33.32, 16.93, 0.204, 11, 4.78, 20.83, 0.796, 2, 25, 3.37, 1.29, 0.62, 11, -25.16, 5.19, 0.38, 2, 25, 8.32, 3.75, 0.484, 11, -20.22, 7.65, 0.516, 2, 25, 13.04, -0.44, 0.236, 11, -15.5, 3.46, 0.764, 2, 25, 10.4, -2.21, 0.304, 11, -18.14, 1.69, 0.696, 2, 25, 28.99, -3.02, 0.14, 11, 0.45, 0.87, 0.86, 2, 25, 26.09, 4.94, 0.048, 11, -2.45, 8.84, 0.952, 2, 25, 16.04, 2.19, 0.132, 11, -12.5, 6.09, 0.868, 2, 25, 6.92, -6.04, 0.516, 11, -21.62, -2.14, 0.484, 2, 25, 7.04, 8.43, 0.62, 11, -21.5, 12.33, 0.38, 2, 25, 8.59, 0.6, 0.4036, 11, -19.95, 4.5, 0.5964], "hull": 13, "edges": [14, 12, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 24, 22, 24, 22, 20, 20, 18, 14, 16, 18, 16, 26, 28, 28, 30, 30, 32, 32, 26], "width": 37, "height": 29}}, "Head_Turtle_B": {"Head_Turtle_B": {"type": "mesh", "uvs": [0.9037, 0.41131, 0.91664, 0.59657, 1, 0.77025, 1, 1, 0.8234, 1, 0.6834, 0.95394, 0.52045, 1, 0.09286, 1, 0, 1, 0, 0.38236, 0.1887, 0, 0.50253, 0, 0.80341, 0, 0.26704, 0.705, 0.49098, 0.54174, 0.74395, 0.55658, 0.82274, 0.66047], "triangles": [13, 9, 14, 0, 11, 12, 11, 0, 14, 9, 10, 14, 14, 10, 11, 6, 13, 14, 15, 14, 0, 4, 2, 3, 2, 4, 16, 16, 1, 2, 16, 15, 1, 15, 0, 1, 4, 5, 16, 5, 15, 16, 5, 14, 15, 14, 5, 6, 6, 7, 13, 8, 9, 13, 7, 8, 13], "vertices": [3, 25, 30.4, -5.74, 0.1803, 11, 1.86, -1.84, 0.5197, 10, 25.38, 5.3, 0.3, 3, 25, 30.59, -9.28, 0.19061, 11, 2.05, -5.38, 0.54939, 10, 25.87, 1.79, 0.26, 3, 25, 33.18, -12.77, 0.13267, 11, 4.64, -8.87, 0.602, 10, 28.75, -1.47, 0.26533, 3, 25, 32.87, -17.13, 0.01997, 11, 4.33, -13.23, 0.79603, 10, 28.82, -5.84, 0.184, 3, 25, 26.88, -16.7, 0.04422, 11, -1.66, -12.8, 0.75178, 10, 22.82, -5.93, 0.204, 3, 25, 22.2, -15.49, 0.15205, 11, -6.34, -11.59, 0.56661, 10, 18.04, -5.12, 0.28133, 3, 25, 16.61, -15.96, 0.19483, 11, -11.93, -12.07, 0.56355, 10, 12.52, -6.08, 0.24162, 3, 25, 2.11, -14.93, 0.866, 11, -26.43, -11.03, 0.04467, 10, -2.02, -6.3, 0.08933, 3, 25, -1.04, -14.7, 0.81, 11, -29.58, -10.8, 0.06333, 10, -5.18, -6.35, 0.12667, 3, 25, -0.2, -3, 0.44932, 11, -28.74, 0.9, 0.29334, 10, -5.35, 5.38, 0.25733, 3, 25, 6.71, 3.79, 0.25, 11, -21.83, 7.69, 0.25, 10, 0.95, 12.75, 0.5, 3, 25, 17.36, 3.03, 0.25, 11, -11.18, 6.93, 0.25, 10, 11.62, 12.91, 0.5, 3, 25, 27.56, 2.3, 0.25, 11, -0.98, 6.2, 0.25, 10, 21.85, 13.06, 0.5, 3, 25, 8.41, -9.76, 0.362, 11, -20.12, -5.86, 0.21267, 10, 3.82, -0.61, 0.42533, 3, 25, 16.23, -7.21, 0.024, 11, -12.31, -3.31, 0.39732, 10, 11.38, 2.61, 0.57868, 2, 11, -3.75, -4.2, 0.61217, 10, 19.99, 2.46, 0.38783, 3, 25, 27.32, -10.26, 0.07902, 11, -1.22, -6.36, 0.61565, 10, 22.7, 0.52, 0.30533], "hull": 13, "edges": [18, 20, 20, 22, 22, 24, 24, 0, 0, 2, 6, 4, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 34, "height": 19}}, "Leg1": {"Leg1": {"type": "mesh", "uvs": [0.64928, 0.23937, 0.69322, 0.28708, 0.69636, 0.4729, 0.71833, 0.51308, 1, 0.64365, 1, 1, 0.57081, 1, 0.20983, 0.83199, 0.18472, 0.63863, 0.19414, 0.59594, 0.1502, 0.39505, 0.13136, 0.35488, 0, 0.19417, 0, 0, 0.50175, 0, 0.48606, 0.32474, 0.4892, 0.61101], "triangles": [7, 8, 16, 6, 16, 3, 6, 3, 4, 7, 16, 6, 6, 4, 5, 9, 10, 15, 15, 2, 16, 1, 15, 0, 2, 15, 1, 16, 2, 3, 9, 15, 16, 16, 8, 9, 12, 13, 14, 14, 11, 12, 15, 14, 0, 15, 11, 14, 10, 11, 15], "vertices": [2, 13, 18.51, 6.64, 0.64879, 14, -3.75, 5.56, 0.35121, 3, 13, 21.17, 6.4, 0.34801, 14, -1.64, 7.19, 0.64896, 15, -7.06, 14.59, 0.00302, 2, 14, 6.72, 7.47, 0.7439, 15, -0.29, 9.68, 0.2561, 2, 14, 8.51, 8.3, 0.51844, 15, 1.63, 9.24, 0.48156, 2, 14, 14.18, 18.56, 0.02434, 15, 12.4, 13.86, 0.97566, 1, 15, 25.26, 4.29, 1, 1, 15, 16.03, -8.11, 1, 2, 14, 23.23, -9.71, 0.14603, 15, 2.21, -14.01, 0.85397, 3, 13, 20.95, -17.79, 0.02543, 14, 14.55, -10.79, 0.60476, 15, -5.31, -9.54, 0.36981, 3, 13, 19.73, -16.27, 0.05296, 14, 12.62, -10.49, 0.7229, 15, -6.65, -8.12, 0.22414, 3, 13, 11.9, -11.48, 0.49143, 14, 3.62, -12.26, 0.50852, 15, -14.84, -3.99, 5e-05, 2, 13, 10.09, -10.8, 0.64227, 14, 1.82, -12.97, 0.35773, 2, 13, 1.54, -9.57, 0.96186, 14, -5.31, -17.85, 0.03814, 1, 13, -5.02, -3.8, 1, 1, 13, 6.91, 9.76, 1, 2, 13, 17.51, -0.31, 0.39433, 14, 0.21, -0.24, 0.60567, 2, 14, 13.08, 0.14, 0.30256, 15, 0.24, -0.01, 0.69744], "hull": 15, "edges": [26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 10, 12, 14, 12], "width": 36, "height": 45}}, "Leg1_B": {"Leg1_B": {"type": "mesh", "uvs": [1, 0.55229, 0.81614, 0.80914, 0.57214, 1, 0.20614, 1, 0, 1, 0, 0.67429, 0.18931, 0, 0.46697, 0, 0.81193, 0, 1, 0, 0.31552, 0.55872, 0.6058, 0.4945], "triangles": [11, 7, 8, 8, 9, 0, 11, 8, 0, 10, 6, 7, 10, 7, 11, 5, 6, 10, 1, 11, 0, 3, 4, 5, 10, 3, 5, 2, 10, 11, 2, 11, 1, 3, 10, 2], "vertices": [1, 13, 16.89, 0.8, 1, 1, 13, 16.46, -6.41, 1, 1, 13, 13.91, -13.94, 1, 1, 13, 6.29, -21.33, 1, 1, 13, 2, -25.49, 1, 1, 13, -2.31, -21.05, 1, 1, 13, -7.29, -8.03, 1, 1, 13, -1.51, -2.42, 1, 1, 13, 5.67, 4.54, 1, 1, 13, 9.59, 8.34, 1, 1, 13, 2.73, -13.1, 1, 1, 13, 7.92, -6.37, 1], "hull": 10, "edges": [6, 8, 4, 6, 2, 4, 0, 18, 2, 0, 16, 18, 14, 16, 12, 14, 8, 10, 12, 10], "width": 29, "height": 19}}, "Leg1_B2": {"Leg1_B": {"type": "mesh", "uvs": [1, 0.55229, 0.81614, 0.80914, 0.57214, 1, 0.20614, 1, 0, 1, 0, 0.67429, 0.18931, 0, 0.46697, 0, 0.81193, 0, 1, 0, 0.31552, 0.55872, 0.6058, 0.4945], "triangles": [1, 11, 0, 11, 7, 8, 8, 9, 0, 11, 8, 0, 10, 6, 7, 10, 7, 11, 5, 6, 10, 3, 4, 5, 10, 3, 5, 2, 10, 11, 2, 11, 1, 3, 10, 2], "vertices": [2, 16, 18.39, 0.39, 0.00668, 17, 3.82, 2.5, 0.99332, 2, 16, 15.37, -6.18, 0.5215, 17, 4.28, -4.71, 0.4785, 2, 16, 10.25, -12.27, 0.89525, 17, 2.66, -12.5, 0.10475, 2, 16, 0.47, -16.38, 0.99551, 17, -3.99, -20.76, 0.00449, 1, 16, -5.04, -18.7, 1, 1, 16, -7.44, -13, 1, 1, 16, -7.35, 0.94, 1, 1, 16, 0.07, 4.06, 1, 2, 16, 9.29, 7.94, 0.97892, 17, -7.77, 4.84, 0.02108, 2, 16, 14.32, 10.06, 0.89203, 17, -4.35, 9.08, 0.10797, 2, 16, 0.14, -7.42, 0.99622, 17, -8.53, -13.04, 0.00378, 2, 16, 7.42, -3.03, 0.96411, 17, -4.2, -5.71, 0.03589], "hull": 10, "edges": [6, 8, 4, 6, 2, 4, 0, 18, 2, 0, 16, 18, 14, 16, 12, 14, 8, 10, 12, 10], "width": 29, "height": 19}}, "Leg2": {"Leg2": {"type": "mesh", "uvs": [0.57782, 0.08071, 0.62057, 0.13138, 0.67282, 0.37063, 0.70369, 0.4016, 1, 0.59863, 1, 1, 0.65144, 1, 0.25482, 0.86322, 0.2192, 0.62678, 0.21445, 0.56204, 0.13132, 0.34811, 0.09807, 0.30026, 0, 0.17923, 0, 0, 0.49232, 0, 0.41606, 0.16872, 0.38587, 0.58783, 0.82481, 0.85759], "triangles": [17, 3, 4, 7, 8, 16, 16, 2, 3, 16, 3, 17, 6, 16, 17, 7, 16, 6, 17, 4, 5, 6, 17, 5, 15, 14, 0, 11, 12, 15, 10, 11, 15, 16, 9, 10, 15, 16, 10, 8, 9, 16, 15, 0, 1, 15, 1, 2, 16, 15, 2, 15, 13, 14, 12, 13, 15], "vertices": [3, 19, 14.29, 5.32, 0.42826, 20, -2.26, 5.19, 0.55111, 21, -1.54, 14.48, 0.02063, 3, 19, 16.21, 5.49, 0.27329, 20, -0.99, 6.65, 0.6779, 21, 0.34, 14.01, 0.04881, 3, 19, 22.25, 2.64, 0.00984, 20, 5.33, 8.77, 0.51433, 21, 5.09, 9.33, 0.47583, 3, 19, 23.52, 2.86, 0.002, 20, 6.1, 9.81, 0.3654, 21, 6.37, 9.12, 0.6326, 2, 20, 10.75, 19.64, 0.00638, 21, 17.24, 9.44, 0.99362, 1, 21, 22.82, 0.14, 1, 1, 21, 13.25, -5.6, 1, 2, 20, 19.53, -3.65, 0.03146, 21, 0.47, -8.96, 0.96854, 3, 19, 18.27, -12.94, 0.00231, 20, 13.24, -5.23, 0.65268, 21, -3.79, -4.07, 0.345, 3, 19, 16.83, -11.94, 0.01455, 20, 11.51, -5.5, 0.88222, 21, -4.82, -2.65, 0.10323, 2, 19, 10.69, -10.28, 0.30229, 20, 5.93, -8.56, 0.69771, 2, 19, 9.01, -10.27, 0.45336, 20, 4.72, -9.71, 0.54664, 2, 19, 4.49, -10.59, 0.71003, 20, 1.68, -13.07, 0.28997, 2, 19, 0.77, -7.49, 0.80723, 20, -3.15, -13.4, 0.19277, 3, 19, 10.86, 4.61, 0.80965, 20, -4.25, 2.31, 0.18979, 21, -5.01, 14.94, 0.00056, 3, 19, 12.8, -0.18, 0.02839, 20, 0.47, 0.2, 0.97126, 21, -4.76, 9.78, 0.00034, 2, 20, 11.82, 0.02, 0.00978, 21, 0.24, -0.42, 0.99022, 1, 21, 16.03, 0.56, 1], "hull": 15, "edges": [24, 26, 24, 22, 22, 20, 26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 20, 18, 18, 16, 16, 14, 10, 12, 14, 12], "width": 32, "height": 27}}, "Leg3": {"Leg3": {"type": "mesh", "uvs": [0.88574, 0.16016, 0.77134, 0.33503, 0.74054, 0.38015, 0.67894, 0.48169, 0.71414, 0.52964, 1, 0.67067, 1, 1, 0.39294, 1, 0, 0.84272, 0, 0.60015, 0, 0.53528, 0.08494, 0.28708, 0.13334, 0.20246, 0.23454, 0, 0.33574, 0, 0.51175, 0.32096, 0.37175, 0.58301, 0.75374, 0.89913], "triangles": [16, 9, 10, 16, 8, 9, 17, 4, 5, 16, 17, 7, 4, 16, 3, 17, 16, 4, 8, 16, 7, 17, 5, 6, 7, 17, 6, 2, 15, 1, 3, 15, 2, 16, 15, 3, 11, 16, 10, 15, 14, 0, 1, 15, 0, 14, 12, 13, 14, 15, 12, 15, 11, 12, 16, 11, 15], "vertices": [2, 16, 8.72, 10.2, 0.83188, 17, -9.62, 6.13, 0.16812, 3, 16, 15.02, 6.33, 0.3021, 17, -2.23, 6.04, 0.69242, 18, -1.83, 13.97, 0.00548, 3, 16, 16.64, 5.3, 0.09558, 17, -0.31, 5.99, 0.87826, 18, -0.83, 12.33, 0.02615, 2, 17, 3.94, 6.05, 0.74164, 18, 1.53, 8.79, 0.25836, 2, 17, 5.35, 7.57, 0.44909, 18, 3.56, 8.44, 0.55091, 2, 17, 7.76, 16.26, 0.03251, 18, 12.17, 11.13, 0.96749, 1, 18, 22.68, 3.75, 1, 1, 18, 13.96, -8.68, 1, 2, 17, 23.37, -4.39, 0.02649, 18, 3.3, -13.19, 0.97351, 3, 16, 22.31, -14.3, 0.00342, 17, 14.6, -7.94, 0.48636, 18, -4.45, -7.75, 0.51022, 3, 16, 19.81, -13.92, 0.01772, 17, 12.26, -8.89, 0.67661, 18, -6.52, -6.3, 0.30568, 3, 16, 10.56, -10.34, 0.3943, 17, 2.49, -10.56, 0.60555, 18, -13.22, 1, 0.00015, 2, 16, 7.49, -8.65, 0.65599, 17, -1.02, -10.68, 0.34401, 2, 16, 0.07, -4.95, 0.98799, 17, -9.29, -11.3, 0.01201, 2, 16, 0.45, -2.45, 0.99618, 17, -10.24, -8.96, 0.00382, 2, 16, 13.49, 0, 0.95012, 17, -0.3, -0.18, 0.04988, 2, 17, 10.49, 0.42, 0.23901, 18, 0.35, 0.24, 0.76099, 1, 18, 15.93, 0.97, 1], "hull": 15, "edges": [26, 24, 24, 22, 22, 20, 18, 20, 16, 18, 12, 14, 16, 14, 26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 6, 8, 12, 10, 8, 10], "width": 25, "height": 39}}, "Leg4": {"Leg2": {"type": "mesh", "uvs": [0.57782, 0.08071, 0.62057, 0.13138, 0.67282, 0.37063, 0.70369, 0.4016, 1, 0.59863, 1, 1, 0.65144, 1, 0.25482, 0.86322, 0.2192, 0.62678, 0.21445, 0.56204, 0.13132, 0.34811, 0.09807, 0.30026, 0, 0.17923, 0, 0, 0.49232, 0, 0.41606, 0.16872, 0.38587, 0.58783, 0.82481, 0.85759], "triangles": [17, 3, 4, 7, 8, 16, 16, 2, 3, 16, 3, 17, 6, 16, 17, 7, 16, 6, 17, 4, 5, 6, 17, 5, 15, 14, 0, 11, 12, 15, 10, 11, 15, 16, 9, 10, 15, 16, 10, 8, 9, 16, 15, 0, 1, 15, 1, 2, 16, 15, 2, 15, 13, 14, 12, 13, 15], "vertices": [3, 26, 14.29, 5.32, 0.42826, 27, -2.26, 5.19, 0.55111, 28, -1.54, 14.48, 0.02063, 3, 26, 16.21, 5.49, 0.27329, 27, -0.99, 6.65, 0.6779, 28, 0.34, 14.01, 0.04881, 3, 26, 22.25, 2.64, 0.00984, 27, 5.33, 8.77, 0.51433, 28, 5.09, 9.33, 0.47583, 3, 26, 23.52, 2.86, 0.002, 27, 6.1, 9.81, 0.3654, 28, 6.37, 9.12, 0.6326, 2, 27, 10.75, 19.64, 0.00638, 28, 17.24, 9.44, 0.99362, 1, 28, 22.82, 0.14, 1, 1, 28, 13.25, -5.6, 1, 2, 27, 19.53, -3.65, 0.03146, 28, 0.47, -8.96, 0.96854, 3, 26, 18.27, -12.94, 0.00231, 27, 13.24, -5.23, 0.65268, 28, -3.79, -4.07, 0.345, 3, 26, 16.83, -11.94, 0.01455, 27, 11.51, -5.5, 0.88222, 28, -4.82, -2.65, 0.10323, 2, 26, 10.69, -10.28, 0.30229, 27, 5.93, -8.56, 0.69771, 2, 26, 9.01, -10.27, 0.45336, 27, 4.72, -9.71, 0.54664, 2, 26, 4.49, -10.59, 0.71003, 27, 1.68, -13.07, 0.28997, 2, 26, 0.77, -7.49, 0.80723, 27, -3.15, -13.4, 0.19277, 3, 26, 10.86, 4.61, 0.80965, 27, -4.25, 2.31, 0.18979, 28, -5.01, 14.94, 0.00056, 3, 26, 12.8, -0.18, 0.02839, 27, 0.47, 0.2, 0.97126, 28, -4.76, 9.78, 0.00034, 2, 27, 11.82, 0.02, 0.00978, 28, 0.24, -0.42, 0.99022, 1, 28, 16.03, 0.56, 1], "hull": 15, "edges": [24, 26, 24, 22, 22, 20, 26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 20, 18, 18, 16, 16, 14, 10, 12, 14, 12], "width": 32, "height": 27}}, "Neck": {"Neck": {"type": "mesh", "uvs": [0.94483, 0.07921, 0.86088, 0.48987, 1, 0.90054, 1, 1, 0.70167, 1, 0.36588, 1, 0, 1, 0, 0.5852, 0, 0.13421, 0.27615, 0.08287, 0.60904, 0, 0.28483, 0.53387, 0.5743, 0.51554], "triangles": [4, 2, 3, 5, 12, 4, 4, 1, 2, 4, 12, 1, 5, 7, 11, 5, 11, 12, 5, 6, 7, 7, 8, 11, 8, 9, 11, 11, 9, 12, 12, 10, 1, 12, 9, 10, 1, 10, 0], "vertices": [1, 7, 34.12, 14.98, 1, 1, 7, 31.14, 2.6, 1, 1, 7, 36.65, -9.62, 1, 1, 7, 36.7, -12.61, 1, 1, 7, 25.36, -12.81, 1, 1, 7, 12.61, -13.03, 1, 1, 7, -1.3, -13.27, 1, 1, 7, -1.51, -0.83, 1, 1, 7, -1.75, 12.7, 1, 1, 7, 8.71, 14.42, 1, 1, 7, 21.32, 17.13, 1, 1, 7, 9.28, 0.9, 1, 1, 7, 20.27, 1.64, 1], "hull": 11, "edges": [16, 18, 18, 20, 20, 0, 0, 2, 6, 4, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 38, "height": 30}}, "Sparkle": {"Sparkle": {"x": 0.09, "y": 0.07, "width": 46, "height": 49}}, "Sparkle2": {"Sparkle": {"x": 0.09, "y": 0.07, "width": 46, "height": 49}}, "Sparkle3": {"Sparkle": {"x": 0.09, "y": 0.07, "width": 46, "height": 49}}, "Tail_L": {"Tail_L": {"x": 5.87, "y": 0.19, "rotation": -136.71, "width": 19, "height": 18}}, "body2": {"body2": {"x": 2.43, "y": -26.45, "width": 37, "height": 15}}}}, "animations": {"animation": {"slots": {"Fx/Comp 1_2": {"attachment": [{"time": 1.3333, "name": "Fx/Comp 1_00002"}, {"time": 1.3667, "name": "Fx/Comp 1_00003"}, {"time": 1.4, "name": "Fx/Comp 1_00004"}, {"time": 1.4333, "name": "Fx/Comp 1_00005"}, {"time": 1.4667, "name": "Fx/Comp 1_00007"}, {"time": 1.5, "name": "Fx/Comp 1_00009"}, {"time": 1.5333, "name": "Fx/Comp 1_00011"}, {"time": 1.5667, "name": "Fx/Comp 1_00013"}, {"time": 1.6, "name": "Fx/Comp 1_00015"}, {"time": 1.6333, "name": "Fx/Comp 1_00017"}, {"time": 1.6667, "name": "Fx/Comp 1_00019"}, {"time": 1.7, "name": "Fx/Comp 1_00021"}, {"time": 1.7667, "name": null}]}, "Fx/Comp 1_3": {"attachment": [{"time": 0.0667, "name": "Fx/Comp 1_00002"}, {"time": 0.1333, "name": "Fx/Comp 1_00003"}, {"time": 0.1667, "name": "Fx/Comp 1_00004"}, {"time": 0.2333, "name": "Fx/Comp 1_00005"}, {"time": 0.3, "name": "Fx/Comp 1_00007"}, {"time": 0.3667, "name": "Fx/Comp 1_00009"}, {"time": 0.4333, "name": "Fx/Comp 1_00011"}, {"time": 0.4667, "name": "Fx/Comp 1_00013"}, {"time": 0.5333, "name": "Fx/Comp 1_00015"}, {"time": 0.6, "name": "Fx/Comp 1_00017"}, {"time": 0.6667, "name": "Fx/Comp 1_00019"}, {"time": 0.7, "name": "Fx/Comp 1_00021"}, {"time": 0.8333, "name": null}]}, "Fx/Comp 1_4": {"attachment": [{"time": 0.0667, "name": "Fx/Comp 1_00002"}, {"time": 0.1333, "name": "Fx/Comp 1_00003"}, {"time": 0.1667, "name": "Fx/Comp 1_00004"}, {"time": 0.2333, "name": "Fx/Comp 1_00005"}, {"time": 0.3, "name": "Fx/Comp 1_00007"}, {"time": 0.3667, "name": "Fx/Comp 1_00009"}, {"time": 0.4333, "name": "Fx/Comp 1_00011"}, {"time": 0.4667, "name": "Fx/Comp 1_00013"}, {"time": 0.5333, "name": "Fx/Comp 1_00015"}, {"time": 0.6, "name": "Fx/Comp 1_00017"}, {"time": 0.6667, "name": "Fx/Comp 1_00019"}, {"time": 0.7, "name": "Fx/Comp 1_00021"}, {"time": 0.8333, "name": null}]}, "Sparkle": {"color": [{"time": 0.8333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 0.8333, "name": "Sparkle"}]}, "Sparkle2": {"color": [{"time": 0.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Sparkle"}]}, "Sparkle3": {"color": [{"time": 1.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 1.0667, "name": "Sparkle"}]}}, "bones": {"Body": {"translate": [{"time": 0, "x": 0, "y": -4.37, "curve": [0, 0.25, 0.363, 0.59]}, {"time": 0.2, "x": 0, "y": -10.36, "curve": [0.213, 0.28, 0.571, 0.64]}, {"time": 0.8, "x": 0, "y": -8.58, "curve": [0.213, 0.28, 0.571, 0.64]}, {"time": 1.4, "x": 0, "y": -3.78, "curve": [0, 0.22, 0.205, 0.65]}, {"time": 1.7, "x": 0, "y": -9.28, "curve": [0.333, 0.33, 0.666, 0.67]}, {"time": 2, "x": 0, "y": -4.37}]}, "Translate1": {"translate": [{"time": 0, "x": 1.59, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 1.59, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 3.8, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.59, "y": 0}]}, "Translate4": {"translate": [{"time": 0, "x": -21.81, "y": 1.82, "curve": "stepped"}, {"time": 0.2667, "x": -21.81, "y": 1.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": -1.52, "y": -4.8, "curve": "stepped"}, {"time": 1.7, "x": -1.52, "y": -4.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -21.81, "y": 1.82}]}, "Leg10": {"translate": [{"time": 0, "x": -3.59, "y": -9.51}, {"time": 0.3, "x": 0, "y": -9.51}, {"time": 2, "x": -3.59, "y": -9.51}]}, "Leg7": {"translate": [{"time": 0, "x": 0, "y": -0.79}]}, "Translate3": {"translate": [{"time": 0, "x": 15.24, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 12.16, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 8.74, "y": 0, "curve": [0.28, 0, 0.623, 0.39]}, {"time": 1.5333, "x": 9.18, "y": 0, "curve": [0.326, 0.31, 0.757, 1]}, {"time": 2, "x": 15.24, "y": 0}]}, "Translate2": {"translate": [{"time": 0, "x": 2.13, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 2.13, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": -17.84, "y": 2.85, "curve": "stepped"}, {"time": 1.7, "x": -17.84, "y": 2.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 2.13, "y": 0}]}, "IK_Toe1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": -1.75, "curve": [0, 0.12, 0.478, 0.59]}, {"time": 0.7333, "angle": -2.32, "curve": [0.38, 0.54, 0.787, 1]}, {"time": 1.0667, "angle": -11.9, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -1.34}, {"time": 0.4, "x": 0, "y": -0.33, "curve": [0, 0.12, 0.478, 0.59]}, {"time": 0.7333, "x": 0, "y": 1.78, "curve": [0.38, 0.54, 0.787, 1]}, {"time": 1.0667, "x": 0, "y": 5.32, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": -1.34}]}, "IK_Toe4": {"rotate": [{"time": 0.2667, "angle": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.9667, "angle": 25.75, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0.2667, "x": 0, "y": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.9667, "x": 0, "y": 7.9, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "x": 0, "y": 0}]}, "IK_Toe2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -8.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -4.91, "curve": [0, 0.1, 0.75, 1]}, {"time": 1.8, "angle": 1.59, "curve": [0, 0.1, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -2.25, "y": -2.25, "curve": [0.316, 0, 0.65, 0.35]}, {"time": 0.0333, "x": -2.25, "y": -1.84, "curve": [0.307, 0.18, 0.643, 0.53]}, {"time": 0.1, "x": -2.25, "y": -2.31, "curve": [0.317, 0.27, 0.653, 0.61]}, {"time": 0.1333, "x": -2.25, "y": -2.94, "curve": [0.343, 0.37, 0.757, 1]}, {"time": 0.5333, "x": -2.25, "y": -2.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0, "curve": [0, 0.1, 0.75, 1]}, {"time": 2, "x": -2.25, "y": -2.25}]}, "IK_Toe3": {"rotate": [{"time": 0, "angle": 5.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -3.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 6.01, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 1.6333, "angle": -3.52, "curve": [0.361, 0.44, 0.755, 1]}, {"time": 2, "angle": 5.09}], "translate": [{"time": 0, "x": -15.96, "y": -2.13, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.0667, "x": -17.76, "y": -6.87, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 1.4, "x": -17.76, "y": -5.07, "curve": [0.295, 0, 0.632, 0.37]}, {"time": 1.6333, "x": -17.35, "y": -2.61, "curve": [0.303, 0.22, 0.644, 0.58]}, {"time": 2, "x": -15.96, "y": -2.13}]}, "3D": {"translate": [{"time": 0, "x": 62.53, "y": -4.34, "curve": [0.382, 0.58, 0.734, 1]}, {"time": 0.4333, "x": 135.48, "y": 27.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -137.56, "y": -91.01, "curve": [0.243, 0, 0.651, 0.61]}, {"time": 2, "x": 62.53, "y": -4.34}]}, "3D_Head": {"translate": [{"time": 0, "x": 12.5, "y": -1.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -33.36, "y": 3.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 12.5, "y": -1.13}]}, "Neck": {"rotate": [{"time": 0, "angle": -0.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -0.79, "curve": [0.32, 0, 0.653, 0.34]}, {"time": 2, "angle": -0.01}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -6.23, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Neck2": {"rotate": [{"time": 0, "angle": -1.2, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.3, "angle": -1.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "angle": 2.8, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 2, "angle": -1.2}]}, "Head": {"rotate": [{"time": 0, "angle": -2.43, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.0333, "angle": -5.74}, {"time": 0.1, "angle": -6.34}, {"time": 0.1667, "angle": -7.37}, {"time": 0.6, "angle": 0}, {"time": 1.5, "angle": -3.97}, {"time": 1.5333, "angle": -5.74}, {"time": 1.6, "angle": -8.89}, {"time": 1.6667, "angle": -7.37}, {"time": 2, "angle": -2.43}], "translate": [{"time": 0, "x": -0.7, "y": 0.07, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1.5, "x": -1.2, "y": 0.13, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 2, "x": -0.7, "y": 0.07}]}, "Head_Turtle_B": {"rotate": [{"time": 0, "angle": -19.54, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.9, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -19.54}]}, "Leg4": {"translate": [{"time": 0, "x": 0, "y": -1.86, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": -1.86}, {"time": 1.4, "x": 4.63, "y": -3.91}, {"time": 1.6333, "x": 2.83, "y": 0.49}, {"time": 2, "x": 0, "y": -1.86}]}, "Tail_L": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 16.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Leg1": {"translate": [{"time": 0, "x": 0, "y": 3.59, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 3.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 3.2, "y": 3.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 2.1, "y": 3.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 3.59}]}, "All": {"translate": [{"time": 0.3, "x": 0, "y": 0}, {"time": 1.5, "x": 4.55, "y": 0}, {"time": 2, "x": 0, "y": 0}]}, "Fx/Comp 1_3": {"rotate": [{"time": 0.0667, "angle": -1.89}], "translate": [{"time": 0.0667, "x": -47.13, "y": -53.51}], "scale": [{"time": 0.0667, "x": 0.65, "y": 0.65}]}, "Fx/Comp 1_4": {"rotate": [{"time": 0.0667, "angle": -1.89}], "translate": [{"time": 0.0667, "x": 36.69, "y": -44.89}], "scale": [{"time": 0.0667, "x": 0.697, "y": 0.697}]}, "Sparkle": {"rotate": [{"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 78.14}], "scale": [{"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.008, "y": 1.008}]}, "IK_Leg3": {"translate": [{"time": 0.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 5.43, "y": 1.3, "curve": "stepped"}, {"time": 1.8, "x": 5.43, "y": 1.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Fx/Comp 1_2": {"translate": [{"time": 1.3333, "x": 10.3, "y": -60.63}], "scale": [{"time": 1.3333, "x": 0.721, "y": 0.721}]}, "Sparkle2": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 106.45, "curve": "stepped"}, {"time": 1.1333, "angle": -14.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 125.82}], "translate": [{"time": 0.3333, "x": -20.54, "y": -25.9, "curve": "stepped"}, {"time": 1.1333, "x": -66.14, "y": -9.4}, {"time": 1.4667, "x": -65.62, "y": -9.4}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.008, "y": 1.008, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1.008, "y": 1.008}]}, "Sparkle3": {"rotate": [{"time": 1.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 106.45}], "translate": [{"time": 1.0667, "x": -4.92, "y": -12.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -4.92, "y": -6.5}], "scale": [{"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.008, "y": 1.008}]}}, "deform": {"default": {"Body": {"Body": [{"time": 0, "curve": [0, 0.11, 0.75, 1]}, {"time": 0.4, "offset": 1, "vertices": [-1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499], "curve": [0.25, 0, 1, 0.83]}, {"time": 1.4, "curve": [0, 0.11, 0.75, 1]}, {"time": 1.8, "offset": 1, "vertices": [-1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998], "curve": [0.25, 0, 1, 0.83]}, {"time": 2}]}, "Body_Back": {"Body_Back": [{"time": 0, "offset": 12, "vertices": [0.99199, -0.248, 0.99199, -0.248]}]}, "Leg1": {"Leg1": [{"time": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "vertices": [-2.26473, -1.84747, 1.07246, -2.71881, -1.13215, -2.17642, 1.72983, -1.73962, 0.07066, -2.45226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.36528, -2.11753, 1.90661, -0.99109, 0.71185, -2.02748, -1.66431, -1.29644, 0.7298, -1.97941, 0, 0, 0, 0, 0, 0, 0, 0, -1.58067, -3.01379, 2.39145, -2.42125], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5}]}, "Leg1_B": {"Leg1_B": [{"time": 0, "vertices": [3.31739, -0.43413, 1.40803, -2.03782, 0.94648, 1.83577, 4.59345, 9.02974, 4.30113, 12.6785, 5.47579, 8.7198, 8.71264, 6.55731, 8.5972, 6.97918, 4.42166, 1.90284, 4.00867, 0.17961, 6.81305, 6.45513, 4.34253, 2.28868], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "vertices": [2.7302, -1.26204, 0.82084, -2.86573, -0.96083, -1.35129, 4.25948, 5.90376, 6.95325, 11.16696, 9.19065, 10.38312, 10.68407, 5.81099, 7.82675, 4.32845, 3.10249, -0.28902, 3.42149, -0.6483, 6.39825, 5.98702, 3.75534, 1.46077], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "vertices": [2.7302, -1.26204, 2.0969, -1.19622, 1.44414, -0.94219, 1.85216, 4.45703, -0.39212, 6.26504, 1.65024, 3.20445, 3.06155, 2.45421, 3.28258, 1.29315, 3.10249, -0.28902, 3.42149, -0.6483, 3.96425, 2.95521, 3.75534, 1.46077], "curve": [0.255, 0, 0.62, 0.47]}, {"time": 1.5, "vertices": [4.37646, 0.26285, 3.74315, 0.32867, 3.0904, 0.5827, 3.49841, 5.98192, 4.05191, 9.3019, 5.1809, 5.15539, 4.7078, 3.9791, 4.92883, 2.81804, 4.74875, 1.23587, 5.06774, 0.87659, 5.61051, 4.4801, 5.4016, 2.98566], "curve": [0.367, 0.46, 0.754, 1]}, {"time": 2, "vertices": [3.31739, -0.43413, 1.40803, -2.03782, 0.94648, 1.83577, 4.59345, 9.02974, 4.30113, 12.6785, 5.47579, 8.7198, 8.71264, 6.55731, 8.5972, 6.97918, 4.42166, 1.90284, 4.00867, 0.17961, 6.81305, 6.45513, 4.34253, 2.28868]}]}, "Leg3": {"Leg3": [{"time": 0, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 0.6, "offset": 4, "vertices": [-2.0431, -0.8062, 0.35728, -2.16718, -2.16523, -0.36872, -1.64948, -0.01548, -0.33266, -1.61568, -1.61735, 0.32411, -1.17188, -0.17592, -0.18201, 1.17086, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98279, -0.82145, 1.2209, 1.76496, -1.11259, 0.59988, 0.59405, 1.11569], "curve": "stepped"}, {"time": 0.9, "offset": 4, "vertices": [-2.0431, -0.8062, 0.35728, -2.16718, -2.16523, -0.36872, -1.64948, -0.01548, -0.33266, -1.61568, -1.61735, 0.32411, -1.17188, -0.17592, -0.18201, 1.17086, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98279, -0.82145, 1.2209, 1.76496, -1.11259, 0.59988, 0.59405, 1.11569], "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.5, "offset": 4, "vertices": [-0.89878, -0.38382, 0.23332, -0.94902, -0.8913, -0.40082, -0.84946, -0.76279, 0.61529, -0.96167, -0.8348, -0.77878, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.17356, -2.65929, 2.43434, -1.58834, -1.12279, -2.68109, -2.43193, -1.2596, 0.84952, -2.60367, 0, 0, 0, 0, 0, 0, 0, 0, -1.32352, -0.76521, 0.54095, -1.42986], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Leg1_B2": {"Leg1_B": [{"time": 0, "offset": 4, "vertices": [1.56114, -0.4407, 1.18174, 1.11125, 2.98834, -1.04702, 2.43648, 2.02238, -8.39901, 3.71863, -7.51301, -5.28451, -7.81098, 5.931, -5.58256, 3.4704], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "offset": 12, "vertices": [-11.37438, 1.68011, -6.91319, -9.18725, -9.29594, 7.30314, -5.6659, 5.32699]}, {"time": 1.5, "offset": 4, "vertices": [-1.83615, -1.97079, 1.64776, -2.1308, -3.76402, -1.01904, 0.39664, -3.8793, -3.15254, 2.61803, -3.80656, -1.51741, -3.50772, 5.68187, -2.45273, 3.53939]}, {"time": 1.9, "offset": 12, "vertices": [-3.15254, 2.61803, -3.80656, -1.51741, -3.50772, 5.68187, -2.45273, 3.53939]}, {"time": 2, "offset": 4, "vertices": [1.56114, -0.4407, 1.18174, 1.11125, 2.98834, -1.04702, 2.43648, 2.02238, -8.39901, 3.71863, -7.51301, -5.28451, -7.81098, 5.931, -5.58256, 3.4704]}]}, "Neck": {"Neck": [{"time": 0, "offset": 10, "vertices": [-3.49556, 1.30248, -6.69803, 7.0748]}]}}}}, "without_frame": {"slots": {"BG": {"attachment": [{"time": 0, "name": null}]}, "Fx/Comp 1_2": {"attachment": [{"time": 1.3333, "name": "Fx/Comp 1_00002"}, {"time": 1.3667, "name": "Fx/Comp 1_00003"}, {"time": 1.4, "name": "Fx/Comp 1_00004"}, {"time": 1.4333, "name": "Fx/Comp 1_00005"}, {"time": 1.4667, "name": "Fx/Comp 1_00007"}, {"time": 1.5, "name": "Fx/Comp 1_00009"}, {"time": 1.5333, "name": "Fx/Comp 1_00011"}, {"time": 1.5667, "name": "Fx/Comp 1_00013"}, {"time": 1.6, "name": "Fx/Comp 1_00015"}, {"time": 1.6333, "name": "Fx/Comp 1_00017"}, {"time": 1.6667, "name": "Fx/Comp 1_00019"}, {"time": 1.7, "name": "Fx/Comp 1_00021"}, {"time": 1.7667, "name": null}]}, "Fx/Comp 1_3": {"attachment": [{"time": 0.0667, "name": "Fx/Comp 1_00002"}, {"time": 0.1333, "name": "Fx/Comp 1_00003"}, {"time": 0.1667, "name": "Fx/Comp 1_00004"}, {"time": 0.2333, "name": "Fx/Comp 1_00005"}, {"time": 0.3, "name": "Fx/Comp 1_00007"}, {"time": 0.3667, "name": "Fx/Comp 1_00009"}, {"time": 0.4333, "name": "Fx/Comp 1_00011"}, {"time": 0.4667, "name": "Fx/Comp 1_00013"}, {"time": 0.5333, "name": "Fx/Comp 1_00015"}, {"time": 0.6, "name": "Fx/Comp 1_00017"}, {"time": 0.6667, "name": "Fx/Comp 1_00019"}, {"time": 0.7, "name": "Fx/Comp 1_00021"}, {"time": 0.8333, "name": null}]}, "Fx/Comp 1_4": {"attachment": [{"time": 0.0667, "name": "Fx/Comp 1_00002"}, {"time": 0.1333, "name": "Fx/Comp 1_00003"}, {"time": 0.1667, "name": "Fx/Comp 1_00004"}, {"time": 0.2333, "name": "Fx/Comp 1_00005"}, {"time": 0.3, "name": "Fx/Comp 1_00007"}, {"time": 0.3667, "name": "Fx/Comp 1_00009"}, {"time": 0.4333, "name": "Fx/Comp 1_00011"}, {"time": 0.4667, "name": "Fx/Comp 1_00013"}, {"time": 0.5333, "name": "Fx/Comp 1_00015"}, {"time": 0.6, "name": "Fx/Comp 1_00017"}, {"time": 0.6667, "name": "Fx/Comp 1_00019"}, {"time": 0.7, "name": "Fx/Comp 1_00021"}, {"time": 0.8333, "name": null}]}, "Sparkle": {"color": [{"time": 0.8333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 0.8333, "name": "Sparkle"}]}, "Sparkle2": {"color": [{"time": 0.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Sparkle"}]}, "Sparkle3": {"color": [{"time": 1.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 1.0667, "name": "Sparkle"}]}}, "bones": {"Body": {"translate": [{"time": 0, "x": 0, "y": -4.37, "curve": [0, 0.25, 0.363, 0.59]}, {"time": 0.2, "x": 0, "y": -10.36, "curve": [0.213, 0.28, 0.571, 0.64]}, {"time": 0.8, "x": 0, "y": -8.58, "curve": [0.213, 0.28, 0.571, 0.64]}, {"time": 1.4, "x": 0, "y": -3.78, "curve": [0, 0.22, 0.205, 0.65]}, {"time": 1.7, "x": 0, "y": -9.28, "curve": [0.333, 0.33, 0.666, 0.67]}, {"time": 2, "x": 0, "y": -4.37}]}, "Translate1": {"translate": [{"time": 0, "x": 1.59, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 1.59, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 3.8, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.59, "y": 0}]}, "Translate4": {"translate": [{"time": 0, "x": -21.81, "y": 1.82, "curve": "stepped"}, {"time": 0.2667, "x": -21.81, "y": 1.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": -1.52, "y": -4.8, "curve": "stepped"}, {"time": 1.7, "x": -1.52, "y": -4.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -21.81, "y": 1.82}]}, "Leg10": {"translate": [{"time": 0, "x": -3.59, "y": -9.51}, {"time": 0.3, "x": 0, "y": -9.51}, {"time": 2, "x": -3.59, "y": -9.51}]}, "Leg7": {"translate": [{"time": 0, "x": 0, "y": -0.79}]}, "Translate3": {"translate": [{"time": 0, "x": 15.24, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 12.16, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 8.74, "y": 0, "curve": [0.28, 0, 0.623, 0.39]}, {"time": 1.5333, "x": 9.18, "y": 0, "curve": [0.326, 0.31, 0.757, 1]}, {"time": 2, "x": 15.24, "y": 0}]}, "Translate2": {"translate": [{"time": 0, "x": 2.13, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 2.13, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": -17.84, "y": 2.85, "curve": "stepped"}, {"time": 1.7, "x": -17.84, "y": 2.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 2.13, "y": 0}]}, "IK_Toe1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": -1.75, "curve": [0, 0.12, 0.478, 0.59]}, {"time": 0.7333, "angle": -2.32, "curve": [0.38, 0.54, 0.787, 1]}, {"time": 1.0667, "angle": -11.9, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -1.34}, {"time": 0.4, "x": 0, "y": -0.33, "curve": [0, 0.12, 0.478, 0.59]}, {"time": 0.7333, "x": 0, "y": 1.78, "curve": [0.38, 0.54, 0.787, 1]}, {"time": 1.0667, "x": 0, "y": 5.32, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": -1.34}]}, "IK_Toe4": {"rotate": [{"time": 0.2667, "angle": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.9667, "angle": 25.75, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0.2667, "x": 0, "y": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.9667, "x": 0, "y": 7.9, "curve": [0.25, 0, 1, 0.75]}, {"time": 1.4, "x": 0, "y": 0}]}, "IK_Toe2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -8.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -4.91, "curve": [0, 0.1, 0.75, 1]}, {"time": 1.8, "angle": 1.59, "curve": [0, 0.1, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -2.25, "y": -2.25, "curve": [0.316, 0, 0.65, 0.35]}, {"time": 0.0333, "x": -2.25, "y": -1.84, "curve": [0.307, 0.18, 0.643, 0.53]}, {"time": 0.1, "x": -2.25, "y": -2.31, "curve": [0.317, 0.27, 0.653, 0.61]}, {"time": 0.1333, "x": -2.25, "y": -2.94, "curve": [0.343, 0.37, 0.757, 1]}, {"time": 0.5333, "x": -2.25, "y": -2.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0, "curve": [0, 0.1, 0.75, 1]}, {"time": 2, "x": -2.25, "y": -2.25}]}, "IK_Toe3": {"rotate": [{"time": 0, "angle": 5.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -3.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 6.01, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 1.6333, "angle": -3.52, "curve": [0.361, 0.44, 0.755, 1]}, {"time": 2, "angle": 5.09}], "translate": [{"time": 0, "x": -15.96, "y": -2.13, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.0667, "x": -17.76, "y": -6.87, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 1.4, "x": -17.76, "y": -5.07, "curve": [0.295, 0, 0.632, 0.37]}, {"time": 1.6333, "x": -17.35, "y": -2.61, "curve": [0.303, 0.22, 0.644, 0.58]}, {"time": 2, "x": -15.96, "y": -2.13}]}, "3D": {"translate": [{"time": 0, "x": 62.53, "y": -4.34, "curve": [0.382, 0.58, 0.734, 1]}, {"time": 0.4333, "x": 135.48, "y": 27.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -137.56, "y": -91.01, "curve": [0.243, 0, 0.651, 0.61]}, {"time": 2, "x": 62.53, "y": -4.34}]}, "3D_Head": {"translate": [{"time": 0, "x": 12.5, "y": -1.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -33.36, "y": 3.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 12.5, "y": -1.13}]}, "Neck": {"rotate": [{"time": 0, "angle": -0.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -0.79, "curve": [0.32, 0, 0.653, 0.34]}, {"time": 2, "angle": -0.01}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -6.23, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Neck2": {"rotate": [{"time": 0, "angle": -1.2, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.3, "angle": -1.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "angle": 2.8, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 2, "angle": -1.2}]}, "Head": {"rotate": [{"time": 0, "angle": -2.43, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.0333, "angle": -5.74}, {"time": 0.1, "angle": -6.34}, {"time": 0.1667, "angle": -7.37}, {"time": 0.6, "angle": 0}, {"time": 1.5, "angle": -3.97}, {"time": 1.5333, "angle": -5.74}, {"time": 1.6, "angle": -8.89}, {"time": 1.6667, "angle": -7.37}, {"time": 2, "angle": -2.43}], "translate": [{"time": 0, "x": -0.7, "y": 0.07, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1.5, "x": -1.2, "y": 0.13, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 2, "x": -0.7, "y": 0.07}]}, "Head_Turtle_B": {"rotate": [{"time": 0, "angle": -19.54, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.9, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -19.54}]}, "Leg4": {"translate": [{"time": 0, "x": 0, "y": -1.86, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": -1.86}, {"time": 1.4, "x": 4.63, "y": -3.91}, {"time": 1.6333, "x": 2.83, "y": 0.49}, {"time": 2, "x": 0, "y": -1.86}]}, "Tail_L": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 16.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Leg1": {"translate": [{"time": 0, "x": 0, "y": 3.59, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 3.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 3.2, "y": 3.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 2.1, "y": 3.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 3.59}]}, "All": {"translate": [{"time": 0.3, "x": 0, "y": 0}, {"time": 1.5, "x": 4.55, "y": 0}, {"time": 2, "x": 0, "y": 0}]}, "Fx/Comp 1_3": {"rotate": [{"time": 0.0667, "angle": -1.89}], "translate": [{"time": 0.0667, "x": -45.35, "y": -39.24}], "scale": [{"time": 0.0667, "x": 0.65, "y": 0.65}]}, "Fx/Comp 1_4": {"rotate": [{"time": 0.0667, "angle": -1.89}], "translate": [{"time": 0.0667, "x": 39.37, "y": -25.27}], "scale": [{"time": 0.0667, "x": 0.697, "y": 0.697}]}, "Sparkle": {"rotate": [{"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 78.14}], "scale": [{"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.008, "y": 1.008}]}, "IK_Leg3": {"translate": [{"time": 0.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 5.43, "y": 1.3, "curve": "stepped"}, {"time": 1.8, "x": 5.43, "y": 1.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Fx/Comp 1_2": {"translate": [{"time": 1.3333, "x": 18.33, "y": -44.13}], "scale": [{"time": 1.3333, "x": 0.721, "y": 0.721}]}, "Sparkle2": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 106.45, "curve": "stepped"}, {"time": 1.1333, "angle": -14.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 125.82}], "translate": [{"time": 0.3333, "x": -20.54, "y": -25.9, "curve": "stepped"}, {"time": 1.1333, "x": -66.14, "y": -9.4}, {"time": 1.4667, "x": -65.62, "y": -9.4}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.008, "y": 1.008, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1.008, "y": 1.008}]}, "Sparkle3": {"rotate": [{"time": 1.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 106.45}], "translate": [{"time": 1.0667, "x": -4.92, "y": -12.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -4.92, "y": -6.5}], "scale": [{"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.008, "y": 1.008}]}}, "deform": {"default": {"Body": {"Body": [{"time": 0, "curve": [0, 0.11, 0.75, 1]}, {"time": 0.4, "offset": 1, "vertices": [-1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499, 0, -1.00499], "curve": [0.25, 0, 1, 0.83]}, {"time": 1.4, "curve": [0, 0.11, 0.75, 1]}, {"time": 1.8, "offset": 1, "vertices": [-1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998, 0, -1.33998], "curve": [0.25, 0, 1, 0.83]}, {"time": 2}]}, "Body_Back": {"Body_Back": [{"time": 0, "offset": 12, "vertices": [0.99199, -0.248, 0.99199, -0.248]}]}, "Leg1": {"Leg1": [{"time": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "vertices": [-2.26473, -1.84747, 1.07246, -2.71881, -1.13215, -2.17642, 1.72983, -1.73962, 0.07066, -2.45226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.36528, -2.11753, 1.90661, -0.99109, 0.71185, -2.02748, -1.66431, -1.29644, 0.7298, -1.97941, 0, 0, 0, 0, 0, 0, 0, 0, -1.58067, -3.01379, 2.39145, -2.42125], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5}]}, "Leg1_B": {"Leg1_B": [{"time": 0, "vertices": [3.31739, -0.43413, 1.40803, -2.03782, 0.94648, 1.83577, 4.59345, 9.02974, 4.30113, 12.6785, 5.47579, 8.7198, 8.71264, 6.55731, 8.5972, 6.97918, 4.42166, 1.90284, 4.00867, 0.17961, 6.81305, 6.45513, 4.34253, 2.28868], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "vertices": [2.7302, -1.26204, 0.82084, -2.86573, -0.96083, -1.35129, 4.25948, 5.90376, 6.95325, 11.16696, 9.19065, 10.38312, 10.68407, 5.81099, 7.82675, 4.32845, 3.10249, -0.28902, 3.42149, -0.6483, 6.39825, 5.98702, 3.75534, 1.46077], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "vertices": [2.7302, -1.26204, 2.0969, -1.19622, 1.44414, -0.94219, 1.85216, 4.45703, -0.39212, 6.26504, 1.65024, 3.20445, 3.06155, 2.45421, 3.28258, 1.29315, 3.10249, -0.28902, 3.42149, -0.6483, 3.96425, 2.95521, 3.75534, 1.46077], "curve": [0.255, 0, 0.62, 0.47]}, {"time": 1.5, "vertices": [4.37646, 0.26285, 3.74315, 0.32867, 3.0904, 0.5827, 3.49841, 5.98192, 4.05191, 9.3019, 5.1809, 5.15539, 4.7078, 3.9791, 4.92883, 2.81804, 4.74875, 1.23587, 5.06774, 0.87659, 5.61051, 4.4801, 5.4016, 2.98566], "curve": [0.367, 0.46, 0.754, 1]}, {"time": 2, "vertices": [3.31739, -0.43413, 1.40803, -2.03782, 0.94648, 1.83577, 4.59345, 9.02974, 4.30113, 12.6785, 5.47579, 8.7198, 8.71264, 6.55731, 8.5972, 6.97918, 4.42166, 1.90284, 4.00867, 0.17961, 6.81305, 6.45513, 4.34253, 2.28868]}]}, "Leg3": {"Leg3": [{"time": 0, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 0.6, "offset": 4, "vertices": [-2.0431, -0.8062, 0.35728, -2.16718, -2.16523, -0.36872, -1.64948, -0.01548, -0.33266, -1.61568, -1.61735, 0.32411, -1.17188, -0.17592, -0.18201, 1.17086, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98279, -0.82145, 1.2209, 1.76496, -1.11259, 0.59988, 0.59405, 1.11569], "curve": "stepped"}, {"time": 0.9, "offset": 4, "vertices": [-2.0431, -0.8062, 0.35728, -2.16718, -2.16523, -0.36872, -1.64948, -0.01548, -0.33266, -1.61568, -1.61735, 0.32411, -1.17188, -0.17592, -0.18201, 1.17086, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98279, -0.82145, 1.2209, 1.76496, -1.11259, 0.59988, 0.59405, 1.11569], "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.5, "offset": 4, "vertices": [-0.89878, -0.38382, 0.23332, -0.94902, -0.8913, -0.40082, -0.84946, -0.76279, 0.61529, -0.96167, -0.8348, -0.77878, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.17356, -2.65929, 2.43434, -1.58834, -1.12279, -2.68109, -2.43193, -1.2596, 0.84952, -2.60367, 0, 0, 0, 0, 0, 0, 0, 0, -1.32352, -0.76521, 0.54095, -1.42986], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Leg1_B2": {"Leg1_B": [{"time": 0, "offset": 4, "vertices": [1.56114, -0.4407, 1.18174, 1.11125, 2.98834, -1.04702, 2.43648, 2.02238, -8.39901, 3.71863, -7.51301, -5.28451, -7.81098, 5.931, -5.58256, 3.4704], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "offset": 12, "vertices": [-11.37438, 1.68011, -6.91319, -9.18725, -9.29594, 7.30314, -5.6659, 5.32699]}, {"time": 1.5, "offset": 4, "vertices": [-1.83615, -1.97079, 1.64776, -2.1308, -3.76402, -1.01904, 0.39664, -3.8793, -3.15254, 2.61803, -3.80656, -1.51741, -3.50772, 5.68187, -2.45273, 3.53939]}, {"time": 1.9, "offset": 12, "vertices": [-3.15254, 2.61803, -3.80656, -1.51741, -3.50772, 5.68187, -2.45273, 3.53939]}, {"time": 2, "offset": 4, "vertices": [1.56114, -0.4407, 1.18174, 1.11125, 2.98834, -1.04702, 2.43648, 2.02238, -8.39901, 3.71863, -7.51301, -5.28451, -7.81098, 5.931, -5.58256, 3.4704]}]}, "Neck": {"Neck": [{"time": 0, "offset": 10, "vertices": [-3.49556, 1.30248, -6.69803, 7.0748]}]}}}}}}