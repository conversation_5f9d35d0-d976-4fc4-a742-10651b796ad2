{"skins": {"default": {"batgioiitem6": {"batgioiitem6": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 6, 3.44, 19.72, 1, 1, 6, 20.32, -2.62, 1, 1, 6, 5.16, -14.08, 1, 1, 6, -11.72, 8.26, 1], "width": 28, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 19}}, "batgioiitem7": {"batgioiitem7": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 5, -10.32, 4.51, 1, 1, 5, -3.34, 18.91, 1, 1, 5, 12.86, 11.05, 1, 1, 5, 5.88, -3.34, 1], "width": 16, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 18}}, "batgioiitem4": {"batgioiitem4": {"triangles": [1, 3, 0, 1, 2, 3], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 4, 22.24, -11.18, 1, 2, 3, -11.41, -9.93, 0.91031, 4, -19.57, -15.2, 0.08969, 1, 3, -7.87, 11.78, 1, 2, 3, 33.58, 5.01, 0.09972, 4, 20.13, 10.72, 0.90028], "width": 42, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 22}}, "batgioiitem5": {"batgioiitem5": {"triangles": [59, 2, 3, 60, 3, 4, 61, 60, 4, 58, 2, 59, 62, 61, 4, 57, 2, 58, 63, 62, 4, 56, 2, 57, 20, 19, 3, 18, 59, 3, 18, 58, 59, 3, 19, 18, 17, 57, 58, 17, 56, 57, 60, 20, 3, 64, 63, 4, 63, 82, 62, 83, 63, 64, 58, 18, 17, 83, 82, 63, 9, 18, 19, 10, 17, 18, 61, 21, 20, 61, 20, 60, 21, 61, 62, 81, 21, 62, 8, 9, 19, 7, 19, 20, 8, 19, 7, 9, 10, 18, 82, 81, 62, 65, 83, 64, 6, 7, 20, 5, 6, 20, 47, 22, 21, 21, 5, 20, 21, 81, 47, 11, 17, 10, 49, 82, 83, 49, 83, 65, 81, 82, 49, 48, 81, 49, 22, 5, 21, 26, 56, 17, 26, 17, 11, 12, 10, 9, 11, 10, 12, 13, 9, 8, 12, 9, 13, 48, 47, 81, 6, 15, 7, 16, 6, 5, 14, 8, 7, 15, 14, 7, 13, 8, 14, 6, 16, 15, 80, 2, 56, 80, 56, 26, 50, 49, 65, 67, 66, 64, 65, 64, 66, 50, 65, 66, 23, 16, 5, 23, 5, 22, 46, 23, 22, 46, 22, 47, 25, 11, 12, 25, 12, 13, 26, 11, 25, 24, 14, 15, 23, 24, 15, 23, 15, 16, 13, 14, 24, 25, 13, 24, 4, 67, 64, 51, 66, 67, 50, 66, 51, 45, 23, 46, 79, 2, 80, 68, 67, 4, 43, 44, 45, 4, 69, 68, 52, 67, 68, 51, 67, 52, 43, 46, 47, 43, 45, 46, 47, 49, 50, 49, 47, 48, 41, 50, 51, 41, 51, 52, 50, 42, 47, 42, 43, 47, 52, 55, 41, 50, 41, 42, 52, 54, 55, 44, 28, 45, 29, 44, 43, 53, 54, 52, 40, 25, 24, 24, 23, 45, 30, 43, 42, 29, 43, 30, 28, 24, 45, 29, 28, 44, 40, 24, 28, 27, 26, 25, 27, 25, 40, 31, 42, 41, 31, 41, 55, 30, 42, 31, 27, 78, 79, 4, 0, 69, 32, 31, 55, 39, 27, 40, 33, 32, 55, 33, 55, 54, 28, 39, 40, 38, 39, 28, 34, 31, 32, 34, 32, 33, 35, 30, 31, 35, 31, 34, 37, 29, 30, 36, 37, 30, 38, 28, 29, 37, 38, 29, 35, 36, 30, 68, 53, 52, 68, 69, 53, 69, 54, 53, 70, 54, 69, 33, 54, 70, 79, 80, 27, 27, 80, 26, 77, 78, 27, 70, 34, 33, 71, 34, 70, 35, 34, 71, 76, 77, 27, 76, 27, 39, 72, 35, 71, 36, 35, 72, 75, 76, 39, 75, 39, 38, 73, 37, 36, 73, 36, 72, 74, 38, 37, 74, 37, 73, 75, 38, 74, 79, 78, 1, 2, 79, 1, 1, 78, 77, 1, 77, 76, 69, 0, 70, 71, 70, 0, 72, 71, 0, 73, 72, 0, 74, 73, 0, 1, 76, 75, 1, 75, 74, 1, 74, 0], "uvs": [1, 1, 0, 1, 0, 0, 0.48463, 0, 1, 0, 0.58122, 0.32481, 0.54138, 0.30352, 0.50395, 0.28862, 0.46894, 0.2801, 0.42789, 0.27904, 0.39288, 0.29394, 0.3627, 0.31416, 0.39771, 0.33333, 0.43272, 0.34184, 0.47135, 0.34823, 0.50395, 0.34929, 0.54017, 0.3461, 0.35545, 0.27691, 0.41097, 0.24709, 0.48463, 0.23965, 0.5607, 0.25242, 0.63313, 0.2801, 0.63917, 0.30884, 0.56673, 0.37271, 0.47498, 0.41529, 0.38322, 0.40465, 0.30112, 0.33013, 0.35787, 0.60371, 0.4955, 0.60265, 0.61261, 0.59945, 0.73213, 0.60265, 0.80699, 0.61436, 0.83234, 0.63565, 0.84441, 0.65055, 0.82872, 0.67929, 0.79733, 0.70271, 0.73093, 0.71868, 0.62106, 0.71655, 0.49188, 0.67504, 0.41219, 0.64204, 0.42077, 0.59999, 0.82389, 0.56858, 0.78043, 0.57604, 0.68867, 0.5622, 0.62348, 0.52175, 0.60899, 0.4632, 0.63917, 0.40039, 0.71161, 0.34504, 0.7925, 0.30991, 0.8251, 0.31736, 0.87822, 0.37058, 0.9241, 0.45468, 0.941, 0.53772, 0.93738, 0.57604, 0.89874, 0.59946, 0.8577, 0.58881, 0.15504, 0.22475, 0.20574, 0.1332, 0.27094, 0.06826, 0.35304, 0.01823, 0.64159, 0.02142, 0.71402, 0.04594, 0.81665, 0.11468, 0.87701, 0.19454, 0.89391, 0.26459, 0.89029, 0.29966, 0.90116, 0.37169, 0.93488, 0.45294, 0.9539, 0.535, 0.9822, 0.62956, 0.97897, 0.71989, 0.92397, 0.82808, 0.81934, 0.91025, 0.69861, 0.95638, 0.53897, 0.95756, 0.38739, 0.92799, 0.23715, 0.85229, 0.12044, 0.74702, 0.02922, 0.6181, 0.0252, 0.49627, 0.09763, 0.3567, 0.79456, 0.29477, 0.82324, 0.27795, 0.8567, 0.26742], "vertices": [1, 2, -9.2, -56.02, 1, 1, 2, -27.17, 23.99, 1, 1, 2, 63.58, 44.36, 1, 1, 2, 72.28, 5.59, 1, 1, 2, 81.54, -35.65, 1, 1, 2, 44.54, -8.76, 1, 1, 2, 45.76, -5.14, 1, 1, 2, 46.44, -1.84, 1, 1, 2, 46.58, 1.14, 1, 1, 2, 45.94, 4.44, 1, 1, 2, 43.96, 6.94, 1, 1, 2, 41.58, 8.94, 1, 1, 2, 40.47, 5.75, 1, 1, 2, 40.33, 2.78, 1, 1, 2, 40.44, -0.44, 1, 1, 2, 40.93, -3.07, 1, 1, 2, 41.87, -5.91, 1, 1, 2, 44.83, 10.28, 1, 1, 2, 48.54, 6.45, 1, 1, 2, 50.53, 0.7, 1, 1, 2, 50.74, -5.64, 1, 1, 2, 49.53, -12, 1, 1, 2, 47.03, -13.07, 1, 1, 2, 39.93, -8.57, 1, 1, 2, 34.42, -2.1, 1, 1, 2, 33.74, 5.46, 1, 1, 2, 39.03, 13.54, 1, 1, 2, 15.22, 3.43, 1, 1, 2, 17.79, -7.56, 1, 1, 2, 20.18, -16.86, 1, 1, 2, 22.04, -26.49, 1, 1, 2, 22.32, -32.72, 1, 1, 2, 20.85, -35.18, 1, 1, 2, 19.71, -36.45, 1, 1, 2, 16.82, -35.78, 1, 1, 2, 14.13, -33.75, 1, 1, 2, 11.49, -28.76, 1, 1, 2, 9.71, -19.93, 1, 1, 2, 11.16, -8.74, 1, 1, 2, 12.72, -1.7, 1, 1, 2, 16.69, -1.53, 1, 1, 2, 25.81, -33.36, 1, 1, 2, 24.36, -30.03, 1, 1, 2, 23.96, -22.41, 1, 1, 2, 26.46, -16.37, 1, 1, 2, 31.52, -14.02, 1, 1, 2, 37.76, -15.15, 1, 1, 2, 44.08, -19.82, 1, 1, 2, 48.72, -25.58, 1, 1, 2, 48.63, -28.34, 1, 1, 2, 44.76, -33.67, 1, 1, 2, 37.95, -39.05, 1, 1, 2, 30.72, -42.1, 1, 1, 2, 27.18, -42.59, 1, 1, 2, 24.36, -39.97, 1, 1, 2, 24.59, -36.47, 1, 1, 2, 45.97, 27.38, 1, 1, 2, 55.18, 25.19, 1, 1, 2, 62.25, 21.29, 1, 1, 2, 68.26, 15.74, 1, 1, 2, 73.16, -7.41, 1, 1, 2, 72.14, -13.72, 1, 1, 2, 67.51, -23.39, 1, 1, 2, 61.16, -29.89, 1, 1, 2, 54.99, -32.69, 1, 1, 2, 51.64, -33.14, 1, 1, 2, 45.17, -35.51, 1, 1, 2, 38.36, -39.87, 1, 1, 2, 31.41, -43.03, 1, 1, 2, 23.8, -47.11, 1, 1, 2, 15.63, -48.68, 1, 1, 2, 4.98, -46.44, 1, 1, 2, -4.31, -39.74, 1, 1, 2, -10.66, -31.02, 1, 1, 2, -13.63, -18.27, 1, 1, 2, -13.67, -5.54, 1, 1, 2, -9.5, 8.03, 1, 1, 2, -2.05, 19.51, 1, 1, 2, 8.01, 29.43, 1, 1, 2, 19, 32.24, 1, 1, 2, 32.96, 29.28, 1, 1, 2, 50.18, -25.42, 1, 1, 2, 52.32, -27.35, 1, 1, 2, 53.98, -29.79, 1], "width": 82, "edges": [0, 2, 2, 4, 0, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 10, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 34, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 54, 54, 80, 80, 56, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 82, 112, 114, 114, 116, 116, 118, 4, 6, 6, 8, 118, 6, 6, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 112, 162, 164, 164, 166, 166, 130], "type": "mesh", "hull": 5, "height": 93}}, "satang4": {"satang4": {"triangles": [92, 18, 93, 92, 91, 19, 94, 14, 11, 94, 11, 95, 95, 11, 96, 94, 93, 14, 91, 90, 20, 90, 89, 21, 21, 89, 22, 96, 10, 97, 97, 10, 8, 11, 13, 12, 89, 88, 22, 22, 88, 24, 24, 88, 87, 23, 22, 24, 87, 25, 24, 91, 92, 94, 92, 93, 94, 91, 94, 90, 90, 95, 55, 90, 55, 89, 90, 94, 95, 85, 26, 25, 89, 55, 54, 8, 98, 97, 10, 96, 11, 25, 87, 85, 95, 96, 55, 55, 96, 65, 10, 9, 8, 89, 54, 88, 88, 54, 53, 65, 56, 55, 65, 66, 56, 56, 66, 47, 66, 57, 47, 96, 97, 65, 88, 53, 87, 97, 64, 65, 97, 98, 64, 8, 6, 98, 8, 7, 6, 55, 35, 54, 55, 36, 35, 55, 56, 36, 36, 56, 29, 53, 35, 34, 53, 54, 35, 98, 6, 63, 98, 63, 64, 62, 6, 67, 6, 62, 63, 53, 52, 87, 52, 86, 87, 87, 86, 85, 64, 45, 65, 66, 45, 46, 66, 65, 45, 64, 44, 45, 63, 43, 64, 64, 43, 44, 53, 34, 52, 52, 34, 33, 56, 47, 29, 46, 37, 66, 66, 37, 57, 63, 42, 43, 63, 62, 42, 44, 39, 45, 45, 38, 46, 45, 39, 38, 43, 40, 44, 44, 40, 39, 42, 41, 43, 43, 41, 40, 46, 38, 37, 47, 57, 58, 35, 30, 31, 34, 31, 32, 34, 35, 31, 29, 30, 36, 35, 36, 30, 29, 47, 48, 38, 58, 37, 58, 48, 47, 29, 48, 30, 37, 58, 57, 6, 68, 67, 6, 5, 68, 42, 62, 61, 42, 61, 41, 61, 62, 67, 48, 58, 59, 39, 59, 38, 58, 38, 59, 33, 51, 52, 52, 51, 86, 41, 61, 40, 34, 32, 33, 49, 30, 48, 59, 39, 60, 39, 40, 60, 40, 61, 60, 86, 51, 85, 70, 48, 59, 30, 49, 31, 70, 49, 48, 60, 61, 68, 59, 60, 70, 69, 60, 68, 68, 61, 67, 32, 31, 50, 60, 69, 70, 49, 70, 72, 70, 71, 72, 49, 72, 74, 72, 73, 74, 74, 75, 49, 50, 31, 49, 50, 49, 75, 32, 50, 33, 33, 50, 51, 50, 76, 77, 75, 76, 50, 51, 84, 85, 84, 82, 85, 85, 82, 26, 26, 80, 27, 80, 26, 82, 84, 51, 83, 68, 5, 69, 51, 50, 83, 83, 50, 81, 81, 50, 77, 69, 5, 70, 84, 83, 82, 70, 5, 4, 83, 81, 82, 78, 81, 77, 70, 4, 71, 71, 4, 3, 82, 81, 80, 81, 79, 80, 81, 78, 79, 72, 71, 2, 80, 79, 27, 71, 3, 2, 72, 2, 73, 79, 78, 27, 73, 1, 74, 73, 2, 1, 78, 28, 27, 78, 77, 28, 74, 0, 75, 74, 1, 0, 77, 76, 28, 75, 0, 76, 76, 0, 28, 18, 92, 19, 19, 91, 20, 18, 17, 93, 16, 14, 17, 17, 14, 93, 16, 15, 14, 14, 13, 11, 20, 90, 21], "uvs": [0.42946, 0.00107, 0.34859, 0.01502, 0.25377, 0.04848, 0.14641, 0.12099, 0.12131, 0.16282, 0.13804, 0.27995, 0.19521, 0.34688, 0.10876, 0.38034, 0.17708, 0.43751, 0.10876, 0.57416, 0.16035, 0.57555, 0.1729, 0.59786, 0.14501, 0.63551, 0.14083, 0.70383, 0.16314, 0.75403, 0.06414, 0.78052, 0.15059, 0.88928, 0.27469, 0.96597, 0.41412, 1, 0.59818, 1, 0.75714, 0.95203, 0.8896, 0.84745, 0.96629, 0.71638, 1, 0.5881, 1, 0.42635, 0.97048, 0.29668, 0.90355, 0.17816, 0.78224, 0.068, 0.61212, 0, 0.45287, 0.34812, 0.47442, 0.32585, 0.50387, 0.3122, 0.5362, 0.30789, 0.57211, 0.31005, 0.54554, 0.33304, 0.51537, 0.35027, 0.4773, 0.34956, 0.37242, 0.34956, 0.35878, 0.33734, 0.34082, 0.33016, 0.32502, 0.32944, 0.30993, 0.33375, 0.30131, 0.34309, 0.31496, 0.35458, 0.32861, 0.35674, 0.34441, 0.35674, 0.36093, 0.35387, 0.42773, 0.34956, 0.45359, 0.33088, 0.49454, 0.31077, 0.55487, 0.28635, 0.60515, 0.29209, 0.60731, 0.33663, 0.58217, 0.37901, 0.53548, 0.4063, 0.4737, 0.40918, 0.44497, 0.37829, 0.38434, 0.35247, 0.36885, 0.33769, 0.34857, 0.3274, 0.32688, 0.31882, 0.30505, 0.32388, 0.29152, 0.34402, 0.29251, 0.37064, 0.31927, 0.39881, 0.35223, 0.40163, 0.37899, 0.37149, 0.28042, 0.31581, 0.27658, 0.28894, 0.28503, 0.25901, 0.29424, 0.22754, 0.29424, 0.17764, 0.31189, 0.13542, 0.34336, 0.09474, 0.38558, 0.07018, 0.44622, 0.05099, 0.49612, 0.04792, 0.55445, 0.05559, 0.60819, 0.07862, 0.6481, 0.12698, 0.65117, 0.17073, 0.63275, 0.19453, 0.64887, 0.21525, 0.62277, 0.24058, 0.62738, 0.25977, 0.66576, 0.30276, 0.64733, 0.32809, 0.65808, 0.40255, 0.65194, 0.49159, 0.61126, 0.55914, 0.55983, 0.61824, 0.51914, 0.65893, 0.44699, 0.67351, 0.40631, 0.66507, 0.38021, 0.63206, 0.35795, 0.58217, 0.33108, 0.53534, 0.30575, 0.46779, 0.28426, 0.40562], "vertices": [1, 8, 102.38, 1.17, 1, 1, 8, 103.25, 12.79, 1, 1, 8, 101.9, 27, 1, 2, 7, 113.84, 77.17, 0.00206, 8, 95.61, 44.29, 0.99794, 2, 7, 107.41, 79.75, 0.00375, 8, 90.71, 49.19, 0.99625, 2, 7, 91.36, 74.78, 0.01751, 8, 74, 50.92, 0.98249, 2, 7, 83.26, 65.26, 0.05905, 8, 62.81, 45.35, 0.94095, 2, 7, 76.63, 76.63, 0.08908, 8, 61.18, 58.41, 0.91092, 2, 7, 70.15, 65.77, 0.12797, 8, 50.95, 50.97, 0.87203, 2, 7, 49.45, 72.28, 0.25854, 8, 34.48, 65.08, 0.74146, 2, 7, 50.41, 65.02, 0.28449, 8, 32.51, 58.03, 0.71551, 2, 7, 47.57, 62.76, 0.3528, 8, 29, 57.07, 0.6472, 2, 7, 41.66, 65.82, 0.44955, 8, 24.78, 62.2, 0.55045, 2, 7, 31.99, 64.88, 0.54909, 8, 15.51, 65.13, 0.45091, 2, 7, 25.45, 60.62, 0.6687, 8, 7.83, 63.79, 0.3313, 2, 7, 19.52, 73.91, 0.75391, 8, 7.59, 78.34, 0.24609, 2, 7, 6.2, 59.35, 0.79475, 8, -10.37, 70.18, 0.20525, 2, 7, -1.76, 40.23, 0.89783, 8, -25.21, 55.72, 0.10217, 2, 7, -3.41, 19.91, 0.98437, 8, -34.7, 37.68, 0.01563, 2, 7, 0.72, -5.9, 0.99881, 8, -41.04, 12.33, 0.00119, 2, 7, 11.01, -27.11, 0.91511, 8, -39.91, -11.22, 0.08489, 2, 7, 28.65, -43.34, 0.97333, 8, -30.06, -33.07, 0.02667, 2, 7, 48.75, -51.15, 0.13298, 8, -14.65, -48.15, 0.86702, 2, 7, 67.49, -53, 0.03118, 8, 1.86, -57.21, 0.96882, 2, 7, 90.17, -49.37, 0.00044, 8, 24.14, -62.78, 0.99956, 1, 8, 43.03, -63.18, 1, 1, 8, 61.66, -58.04, 1, 1, 8, 81.01, -45.13, 1, 1, 8, 96.24, -24.03, 1, 2, 7, 88.87, 29.1, 0.01158, 8, 53.77, 9.89, 0.98842, 2, 7, 92.47, 26.58, 0.006, 8, 56.09, 6.16, 0.994, 2, 7, 95.05, 22.76, 0.00249, 8, 56.96, 1.63, 0.99751, 2, 7, 96.38, 18.32, 0.00077, 8, 56.44, -2.97, 0.99923, 2, 7, 96.88, 13.24, 7e-05, 8, 54.9, -7.84, 0.99993, 2, 7, 93.06, 16.45, 0.00083, 8, 52.65, -3.39, 0.99917, 1, 8, 51.32, 1.36, 1, 2, 7, 89.21, 25.65, 0.00778, 8, 52.73, 6.58, 0.99222, 1, 8, 56.34, 21.03, 1, 1, 8, 58.49, 22.49, 1, 1, 8, 60.1, 24.71, 1, 1, 8, 60.74, 26.87, 1, 1, 8, 60.67, 29.09, 1, 1, 8, 59.68, 30.6, 1, 1, 8, 57.62, 29.12, 1, 1, 8, 56.86, 27.31, 1, 1, 8, 56.31, 25.13, 1, 1, 8, 56.14, 22.76, 1, 2, 7, 88.1, 32.59, 0.01664, 8, 54.43, 13.41, 0.98336, 2, 7, 91.3, 29.39, 0.0092, 8, 56.12, 9.2, 0.9908, 2, 7, 95.04, 24.1, 0.00306, 8, 57.48, 2.87, 0.99694, 2, 7, 99.82, 16.19, 0.00014, 8, 58.76, -6.28, 0.99986, 1, 8, 56.24, -13.01, 1, 1, 8, 50.03, -11.78, 1, 2, 7, 87.44, 10.28, 0.00019, 8, 45.06, -6.85, 0.99981, 2, 7, 82.56, 16.21, 0.00373, 8, 42.91, 0.52, 0.99627, 1, 8, 44.64, 9.13, 1, 1, 8, 49.88, 12.02, 1, 1, 8, 55.53, 19.49, 1, 1, 8, 58.1, 21.11, 1, 1, 8, 60.21, 23.55, 1, 1, 8, 62.14, 26.24, 1, 1, 8, 62.2, 29.42, 1, 1, 8, 59.89, 31.98, 1, 1, 8, 56.19, 32.76, 1, 1, 8, 51.38, 30.05, 1, 1, 8, 49.86, 25.6, 1, 1, 8, 53.09, 20.88, 1, 1, 8, 64.16, 32.54, 1, 2, 7, 93.21, 55.15, 0.02465, 8, 67.99, 32.14, 0.97535, 2, 7, 97.6, 54.64, 0.01609, 8, 71.82, 29.95, 0.98391, 2, 7, 102.22, 54.05, 0.00963, 8, 75.84, 27.6, 0.99037, 2, 7, 109.21, 55.17, 0.00382, 8, 82.71, 25.88, 0.99618, 2, 7, 115.53, 53.64, 0.00114, 8, 87.92, 21.99, 0.99886, 2, 7, 121.94, 50.14, 0.00014, 8, 92.44, 16.25, 0.99986, 2, 7, 126.33, 44.77, 0, 8, 94.37, 9.59, 1, 1, 8, 94.93, 0.58, 1, 1, 8, 93.63, -6.4, 1, 1, 8, 90.57, -14.17, 1, 1, 8, 85.54, -20.78, 1, 1, 8, 77.51, -24.62, 1, 1, 8, 71.37, -23.53, 1, 1, 8, 68.73, -20.17, 1, 1, 8, 65.32, -21.68, 1, 1, 8, 62.73, -17.21, 1, 1, 8, 59.93, -17.19, 1, 1, 8, 52.68, -20.99, 1, 1, 8, 49.83, -17.58, 1, 1, 8, 39.2, -16.5, 1, 1, 8, 27.15, -12.59, 1, 1, 8, 19.24, -4.66, 1, 1, 8, 12.87, 4.46, 1, 1, 8, 8.67, 11.47, 1, 1, 8, 9.14, 21.91, 1, 1, 8, 11.71, 27.23, 1, 1, 8, 17.15, 29.68, 1, 1, 8, 24.79, 31.03, 1, 1, 8, 32.17, 33.12, 1, 1, 8, 42.35, 34.28, 1, 1, 8, 51.65, 35.1, 1], "width": 142, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 58, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 74, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 94, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 114, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 126], "type": "mesh", "hull": 29, "height": 142}}, "free11": {"free11": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-26.32, -70.17, -26.32, 70.83, 23.68, 70.83, 23.68, -70.17], "width": 141, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 50}}, "free12": {"free10": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.98, -20.61, -23.98, 22.39, 48.02, 22.39, 48.02, -20.61], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 72}}, "free13": {"free10": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.98, -20.61, -23.98, 22.39, 48.02, 22.39, 48.02, -20.61], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 72}}, "free14": {"free10": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.98, -20.61, -23.98, 22.39, 48.02, 22.39, 48.02, -20.61], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 72}}, "free10": {"free10": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-23.98, -20.61, -23.98, 22.39, 48.02, 22.39, 48.02, -20.61], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 72}}, "jackpot10": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-21.97, -7.01, -21.97, 6.99, 27.03, 6.99, 27.03, -7.01], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "wild8": {"wild8": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 51, -13.66, -20.67, 1, 1, 51, -15.33, -10.81, 1, 1, 51, -6.46, -9.31, 1, 1, 51, -4.79, -19.17, 1], "width": 10, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 9}}, "wild9": {"wild9": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 51, -9.31, 8.15, 1, 1, 51, -10.81, 17.02, 1, 1, 51, -2.93, 18.36, 1, 1, 51, -1.42, 9.49, 1], "width": 9, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 8}}, "jackpot12": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [15.39, -15.86, -15.61, -15.86, -15.61, 16.14, 15.39, 16.14], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "wild6": {"wild6": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 49, -15.46, -65.57, 1, 1, 49, -5.46, 70.06, 1, 1, 49, 75.33, 64.1, 1, 1, 49, 65.32, -71.53, 1], "width": 136, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 81}}, "jackpot11": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [15.39, -15.86, -15.61, -15.86, -15.61, 16.14, 15.39, 16.14], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "wild7": {"wild7": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 50, 20.14, -33.59, 1, 1, 50, 8.45, 35.43, 1, 1, 50, 43.95, 41.44, 1, 1, 50, 55.63, -27.58, 1], "width": 70, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 36}}, "free15": {"free11": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-26.32, -70.17, -26.32, 70.83, 23.68, 70.83, 23.68, -70.17], "width": 141, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 50}}, "wild4": {"wild4": {"x": 0.23, "width": 149, "y": 1.11, "height": 149}}, "free3": {"free3": {"width": 138, "height": 138}}, "wild5": {"wild5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [66.54, -37.57, -70.46, -37.57, -70.46, -3.57, 66.54, -3.57], "width": 137, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 34}}, "wild2": {"wild2": {"triangles": [6, 5, 7, 7, 5, 8, 5, 4, 8, 8, 4, 9, 4, 3, 9, 9, 3, 10, 3, 2, 10, 2, 1, 10, 11, 1, 0, 11, 10, 1, 0, 12, 11], "uvs": [0.93153, 0, 0.81986, 0.00828, 0.70075, 0.07305, 0.63003, 0.22268, 0.42903, 0.48175, 0.12381, 0.75421, 0, 1, 0.25409, 1, 0.48859, 0.74975, 0.7082, 0.52195, 0.89059, 0.26065, 1, 0.06635, 1, 0], "vertices": [1, 65, -2.85, -6.18, 1, 2, 65, 3.04, -7.69, 0.8141, 66, -5.97, -5.07, 0.1859, 2, 65, 11.15, -4.6, 0.04006, 66, 2.3, -7.72, 0.95994, 2, 66, 15.87, -4.28, 0.08846, 67, 2.98, -4.84, 0.91154, 2, 67, 28.61, -6.88, 0.09136, 68, 4.6, -6.09, 0.90864, 1, 69, 6.4, -7.94, 1, 1, 69, 29.25, -4.52, 1, 1, 69, 23.38, 7.88, 1, 2, 68, 23.37, 9.39, 0.78321, 69, -2.39, 9.69, 0.21679, 2, 67, 26.75, 8.51, 0.54747, 68, -0.3, 8.62, 0.45253, 3, 65, 7.76, 14.86, 0.00643, 66, 11.77, 9.61, 0.38282, 67, 1.27, 9.53, 0.61075, 2, 65, -4.12, 0.72, 0.99813, 66, -6.32, 5.96, 0.00187, 1, 65, -6.29, -4.84, 1], "width": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "type": "mesh", "hull": 13, "height": 90}}, "free1": {"free1": {"width": 138, "height": 138}}, "wild3": {"wild3": {"triangles": [14, 15, 6, 7, 13, 14, 7, 14, 6, 8, 12, 13, 8, 13, 7, 10, 11, 12, 9, 10, 12, 9, 12, 8, 16, 17, 4, 5, 16, 4, 15, 16, 5, 6, 15, 5, 17, 18, 2, 17, 2, 3, 4, 17, 3, 18, 0, 1, 18, 1, 2], "uvs": [0.7664, 0.02272, 0.91289, 0, 0.96399, 0.09387, 1, 0.22016, 0.98443, 0.33756, 0.89245, 0.4763, 0.78343, 0.63995, 0.58243, 0.77158, 0.38143, 0.94589, 0.18384, 1, 0.10549, 1, 0, 0.95123, 0.23494, 0.86407, 0.42572, 0.72355, 0.60287, 0.55991, 0.73574, 0.43895, 0.8175, 0.30199, 0.83113, 0.22194, 0.79706, 0.12233], "vertices": [2, 61, -1.69, -4.86, 0.96689, 62, -8.13, -6.08, 0.03311, 1, 61, -2.61, 4.11, 1, 2, 61, 8.37, 5.11, 0.61607, 62, 0.23, 5.35, 0.38393, 1, 62, 14.56, 7.07, 1, 2, 62, 27.79, 5.79, 0.03014, 63, 3.27, 7.17, 0.96986, 1, 63, 19.84, 6.42, 1, 2, 63, 39.4, 5.51, 0.02061, 64, 4.87, 7.66, 0.97939, 1, 64, 23.88, 7.03, 1, 1, 64, 46.76, 9.28, 1, 1, 64, 58.63, 3.58, 1, 1, 64, 61.39, -0.13, 1, 1, 64, 60.68, -8.42, 1, 1, 64, 44.5, -3.18, 1, 1, 64, 25.05, -3.63, 1, 2, 63, 33.76, -7.27, 0.21336, 64, 3.97, -6.29, 0.78664, 1, 63, 18.42, -3.64, 1, 2, 62, 23.5, -3.95, 0.20896, 63, 2.21, -3.41, 0.79104, 1, 62, 14.48, -2.89, 1, 2, 61, 9.71, -5.17, 0.02281, 62, 3.17, -4.59, 0.97719], "width": 59, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "type": "mesh", "hull": 19, "height": 113}}, "fire30": {"fire30": {"x": 0.46, "width": 171, "y": 59.44, "height": 156}, "fire33": {"x": 0.46, "width": 171, "y": 59.44, "height": 156}, "fire32": {"x": 0.46, "width": 171, "y": 59.44, "height": 156}, "fire31": {"x": 0.46, "width": 171, "y": 59.44, "height": 156}}, "free6": {"free6": {"triangles": [11, 14, 10, 13, 14, 11, 12, 13, 11, 17, 20, 21, 19, 20, 17, 18, 19, 17, 9, 16, 17, 15, 16, 9, 10, 15, 9, 14, 15, 10, 8, 9, 17, 25, 23, 24, 25, 27, 23, 26, 27, 25, 28, 23, 27, 28, 22, 23, 29, 7, 8, 17, 21, 22, 29, 8, 17, 17, 22, 29, 28, 29, 22, 30, 7, 29, 31, 7, 30, 6, 7, 34, 31, 32, 7, 32, 33, 7, 33, 34, 7, 3, 6, 34, 3, 34, 35, 5, 6, 3, 4, 5, 3, 2, 3, 35, 1, 2, 35, 0, 1, 35], "uvs": [0.79152, 0.97363, 0.66802, 1, 0.52436, 1, 0.42607, 0.91523, 0.26477, 0.82874, 0.27233, 0.80403, 0.29249, 0.76472, 0.31392, 0.61421, 0.3051, 0.48505, 0.28493, 0.45809, 0.21688, 0.48505, 0.10347, 0.55581, 0, 0.64005, 0, 0.58613, 0.07827, 0.46595, 0.18286, 0.37947, 0.25091, 0.30871, 0.32778, 0.16494, 0.1753, 0.15258, 0.18916, 0.11103, 0.26099, 0.05374, 0.45001, 0, 0.65668, 0, 0.83436, 0.04701, 0.96668, 0.13911, 1, 0.22222, 1, 0.36711, 0.92888, 0.45921, 0.89989, 0.48842, 0.88351, 0.52436, 0.82806, 0.61871, 0.76379, 0.69845, 0.6655, 0.76809, 0.6277, 0.78494, 0.6151, 0.79954, 0.61636, 0.83548], "vertices": [1, 11, -4.85, -32.41, 1, 1, 11, -4.42, -16.8, 1, 1, 11, 0.17, 0.27, 1, 2, 11, 14.6, 8.9, 0.97719, 12, -20.85, -1.27, 0.02281, 3, 11, 31.28, 24.96, 0.40322, 12, -15.3, 21.21, 0.59654, 18, 33.69, 47.34, 0.00024, 3, 11, 34.33, 23.18, 0.37211, 12, -11.77, 21.33, 0.627, 18, 30.84, 45.25, 0.00089, 3, 11, 38.93, 19.38, 0.24554, 12, -5.86, 20.56, 0.74922, 18, 25.52, 42.55, 0.00524, 3, 11, 58.3, 11.44, 0.0005, 12, 14.77, 24.17, 0.88341, 18, 10.53, 27.93, 0.1161, 2, 12, 31.48, 30.47, 0.44397, 18, 0.28, 13.31, 0.55603, 2, 12, 34.3, 33.94, 0.19491, 18, -0.09, 8.85, 0.80509, 3, 12, 28.27, 40.84, 0.00575, 18, 8.77, 6.55, 0.91824, 19, -7.08, 6.3, 0.07601, 1, 19, 9.94, 5.85, 1, 1, 19, 27.09, 7.62, 1, 1, 19, 22.66, 1.64, 1, 2, 18, 20.48, -6.12, 0.01346, 19, 5.06, -5.96, 0.98654, 1, 18, 2.98, -7.45, 1, 2, 12, 52.76, 44.02, 0.12585, 18, -9.65, -9.89, 0.87415, 2, 12, 74.51, 40.85, 0.43895, 18, -29.4, -19.53, 0.56105, 2, 12, 70.6, 59.27, 0.41878, 18, -15.78, -32.54, 0.58122, 2, 12, 76.58, 59.33, 0.42084, 18, -20.69, -35.96, 0.57916, 2, 12, 86.74, 53.22, 0.44311, 18, -32.52, -36.65, 0.55689, 2, 12, 100.69, 33.2, 0.58963, 18, -55.34, -27.98, 0.41037, 2, 12, 108.2, 8.91, 0.79029, 18, -75.23, -12.16, 0.20971, 2, 12, 108.45, -13.88, 0.91696, 18, -88.3, 6.53, 0.08304, 2, 12, 101.11, -33.19, 0.97444, 18, -93.12, 26.61, 0.02556, 2, 12, 91.36, -40.49, 0.98929, 18, -89.19, 38.13, 0.01071, 2, 12, 72.26, -46.39, 0.9999, 18, -76.74, 53.78, 0.0001, 1, 12, 57.53, -41.78, 1, 1, 12, 52.63, -39.57, 1, 1, 12, 47.29, -39.1, 1, 2, 11, 41.28, -49.47, 0.00017, 12, 32.84, -36.43, 0.99983, 2, 11, 32.71, -38.98, 0.01599, 12, 19.99, -32.13, 0.98401, 2, 11, 26.57, -24.81, 0.15758, 12, 7.24, -23.41, 0.84242, 2, 11, 25.53, -19.71, 0.31373, 12, 3.65, -19.66, 0.68627, 2, 11, 23.98, -17.69, 0.47797, 12, 1.27, -18.77, 0.52203, 2, 11, 19.15, -16.56, 0.79226, 12, -3.43, -20.38, 0.20774], "width": 123, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "type": "mesh", "hull": 36, "height": 138}}, "wild0": {"wild0": {"x": 0.58, "width": 149, "y": 0.2, "height": 149}}, "free7": {"free7": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 12, 10.28, -43.55, 1, 1, 12, -5.95, 9, 1, 1, 12, 70.48, 32.61, 1, 1, 12, 86.72, -19.94, 1], "width": 55, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 80}}, "wild1": {"wild1": {"triangles": [26, 28, 27, 49, 48, 29, 28, 49, 29, 26, 49, 28, 25, 49, 26, 47, 30, 29, 47, 29, 48, 38, 48, 49, 48, 36, 47, 37, 48, 38, 37, 36, 48, 31, 30, 1, 30, 47, 46, 35, 47, 36, 39, 37, 38, 35, 46, 47, 50, 49, 25, 38, 49, 50, 34, 46, 35, 46, 1, 30, 0, 31, 1, 39, 40, 36, 39, 36, 37, 33, 46, 34, 45, 1, 46, 45, 46, 33, 40, 41, 35, 40, 35, 36, 32, 45, 33, 42, 34, 35, 42, 35, 41, 43, 33, 34, 43, 32, 33, 34, 42, 43, 65, 66, 1, 45, 65, 1, 67, 1, 66, 44, 45, 32, 64, 65, 45, 65, 56, 66, 55, 56, 65, 44, 64, 45, 54, 55, 65, 64, 54, 65, 51, 38, 50, 39, 38, 51, 40, 39, 51, 57, 67, 66, 57, 66, 56, 2, 1, 67, 68, 2, 67, 58, 68, 67, 57, 58, 67, 63, 55, 54, 72, 64, 44, 54, 64, 72, 63, 54, 72, 3, 68, 58, 2, 68, 3, 58, 60, 59, 3, 58, 59, 53, 43, 42, 32, 43, 53, 44, 32, 53, 62, 56, 55, 62, 55, 63, 24, 23, 25, 52, 41, 40, 52, 40, 51, 42, 41, 52, 53, 42, 52, 61, 56, 62, 57, 56, 61, 57, 60, 58, 57, 61, 60, 4, 3, 59, 4, 59, 60, 71, 63, 72, 62, 63, 71, 23, 22, 50, 69, 61, 62, 70, 69, 62, 60, 61, 69, 4, 60, 69, 71, 70, 62, 77, 52, 51, 5, 70, 71, 69, 70, 5, 4, 69, 5, 76, 53, 52, 76, 52, 77, 75, 53, 76, 53, 72, 44, 74, 53, 75, 53, 71, 72, 53, 5, 71, 74, 5, 53, 73, 5, 74, 6, 5, 73, 21, 9, 8, 76, 77, 8, 7, 75, 76, 7, 76, 8, 73, 74, 75, 7, 73, 75, 6, 73, 7, 8, 77, 21, 23, 50, 25, 22, 51, 50, 21, 51, 22, 21, 77, 51, 19, 21, 20, 18, 17, 16, 15, 18, 16, 18, 15, 19, 12, 11, 10, 19, 9, 21, 15, 9, 19, 14, 9, 15, 14, 10, 9, 13, 10, 14, 12, 10, 13], "uvs": [0, 0.19797, 0.13388, 0.28591, 0.15881, 0.33756, 0.16711, 0.35099, 0.17842, 0.36933, 0.23205, 0.4562, 0.27256, 0.50924, 0.34267, 0.60416, 0.41746, 0.60137, 0.42993, 0.62231, 0.42285, 0.64071, 0.14774, 0.93384, 0.30619, 1, 0.55499, 1, 0.73738, 0.9366, 0.89868, 0.81287, 1, 0.62965, 1, 0.47193, 0.73316, 0.41054, 0.70259, 0.40865, 0.66464, 0.38882, 0.64144, 0.38221, 0.6425, 0.35671, 0.65515, 0.22921, 0.63828, 0.18954, 0.61298, 0.17443, 0.61087, 0.15176, 0.66569, 0.02521, 0.55288, 0, 0.35785, 0, 0.21341, 0.02521, 0.05106, 0.12154, 0.3039, 0.31373, 0.30471, 0.29769, 0.31611, 0.28165, 0.34134, 0.26998, 0.37308, 0.26196, 0.39832, 0.25904, 0.42681, 0.25685, 0.39995, 0.2729, 0.38122, 0.28602, 0.36088, 0.30206, 0.3389, 0.31373, 0.31774, 0.3181, 0.27785, 0.32467, 0.2746, 0.29914, 0.30301, 0.27539, 0.35022, 0.25137, 0.41948, 0.23571, 0.46913, 0.23862, 0.4789, 0.27946, 0.44064, 0.33196, 0.36902, 0.35821, 0.31936, 0.35383, 0.23309, 0.32977, 0.21681, 0.32539, 0.20134, 0.32758, 0.18588, 0.33269, 0.17855, 0.34508, 0.17932, 0.35317, 0.19219, 0.36123, 0.20165, 0.36081, 0.21374, 0.35609, 0.22723, 0.34678, 0.23816, 0.32773, 0.21738, 0.32009, 0.19475, 0.31898, 0.17459, 0.32425, 0.1639, 0.33925, 0.20684, 0.38898, 0.21971, 0.39078, 0.23676, 0.37648, 0.24017, 0.35064, 0.29027, 0.50046, 0.30562, 0.49199, 0.3219, 0.48851, 0.34423, 0.47949, 0.40764, 0.45282], "vertices": [2, 59, 96.61, 87.19, 0, 60, 67.56, 50.68, 1, 2, 59, 87.28, 67.92, 0, 60, 48.67, 40.58, 1, 2, 59, 80.54, 63.42, 0, 60, 40.58, 40.9, 1, 2, 59, 78.83, 62.01, 0, 60, 38.37, 40.77, 1, 2, 59, 76.5, 60.1, 0, 60, 35.36, 40.59, 1, 2, 59, 65.44, 51.04, 0.00595, 60, 21.09, 39.76, 0.99405, 2, 59, 58.87, 44.52, 0.03871, 60, 11.94, 38.35, 0.96129, 2, 59, 47.06, 33.16, 0.19041, 60, -4.3, 36.11, 0.80959, 2, 59, 49.2, 23.74, 0.43248, 60, -8.11, 27.23, 0.56752, 2, 59, 46.52, 21.62, 0.65827, 60, -11.52, 27.09, 0.34173, 2, 59, 43.75, 22.04, 0.78466, 60, -13.51, 29.06, 0.21534, 1, 59, -4.17, 49.32, 1, 1, 59, -9.85, 27.5, 1, 1, 59, -4.06, -4.07, 1, 1, 59, 9.17, -25.56, 1, 1, 59, 30.44, -42.81, 1, 1, 59, 58.75, -50.91, 1, 1, 59, 81.09, -46.81, 1, 2, 59, 83.58, -11.36, 0.8603, 60, -0.93, -21.37, 0.1397, 2, 59, 83.13, -7.43, 0.71709, 60, 1.02, -17.93, 0.28291, 2, 59, 85.06, -2.1, 0.39614, 60, 5.71, -14.75, 0.60386, 2, 59, 85.45, 1.02, 0.20257, 60, 7.86, -12.46, 0.79743, 2, 59, 89.09, 1.55, 0.07091, 60, 11.11, -14.17, 0.92909, 1, 60, 26.96, -23.58, 1, 1, 60, 33.05, -24.09, 1, 1, 60, 36.43, -22.09, 1, 1, 60, 39.49, -23.25, 1, 1, 60, 52.86, -37.51, 1, 1, 60, 62.43, -25.96, 1, 2, 59, 132.99, 46.93, 0, 60, 73.31, -3.27, 1, 2, 59, 126.05, 64.61, 0, 60, 78.09, 15.1, 1, 2, 59, 108.63, 82.7, 0, 60, 74.63, 39.98, 1, 2, 59, 87.29, 45.63, 0, 60, 35.58, 22.54, 1, 2, 59, 89.59, 45.94, 0, 60, 37.62, 21.44, 1, 2, 59, 92.12, 44.91, 0, 60, 39.06, 19.12, 1, 2, 59, 94.36, 42.01, 0, 60, 39.17, 15.46, 1, 2, 59, 96.24, 38.19, 0, 60, 38.44, 11.26, 1, 2, 59, 97.24, 35.07, 0, 60, 37.41, 8.15, 1, 2, 59, 98.21, 31.51, 0, 60, 36.11, 4.7, 1, 2, 59, 95.31, 34.5, 0, 60, 35.52, 8.82, 1, 2, 59, 93.02, 36.54, 0, 60, 34.86, 11.82, 1, 2, 59, 90.27, 38.7, 0, 60, 33.92, 15.18, 1, 1, 60, 33.63, 18.46, 1, 2, 59, 87, 43.76, 0, 60, 34.24, 21.2, 1, 2, 59, 85.14, 48.65, 0, 60, 35.61, 26.25, 1, 2, 59, 88.68, 49.72, 0, 60, 39.11, 25.04, 1, 2, 59, 92.7, 46.74, 0, 60, 40.61, 20.25, 1, 2, 59, 97.21, 41.37, 0, 60, 41.09, 13.27, 1, 2, 59, 101.04, 32.99, 0, 60, 39.26, 4.23, 1, 1, 60, 36.11, -1.36, 1, 1, 60, 30.27, 0.05, 1, 1, 60, 25.58, 7.76, 1, 1, 60, 26.17, 17.73, 1, 1, 60, 29.51, 23.23, 1, 2, 59, 83.37, 54.19, 0, 60, 37.44, 31.77, 1, 2, 59, 83.61, 56.37, 0, 60, 38.92, 33.39, 1, 2, 59, 82.94, 58.28, 0, 60, 39.5, 35.33, 1, 2, 59, 81.86, 60.11, 0, 60, 39.7, 37.44, 1, 2, 59, 79.94, 60.72, 0, 60, 38.5, 39.07, 1, 2, 59, 78.81, 60.41, 0, 60, 37.4, 39.48, 1, 2, 59, 77.97, 58.57, 0, 60, 35.64, 38.49, 1, 2, 59, 78.25, 57.38, 0, 60, 35.17, 37.36, 1, 1, 60, 35.11, 35.66, 1, 2, 59, 80.83, 54.5, 0, 60, 35.56, 33.51, 1, 2, 59, 83.78, 53.6, 0, 60, 37.43, 31.05, 1, 2, 59, 84.38, 56.44, 0, 60, 39.58, 33, 1, 2, 59, 84.01, 59.34, 0, 60, 40.98, 35.56, 1, 2, 59, 82.79, 61.76, 0, 60, 41.42, 38.23, 1, 2, 59, 80.42, 62.73, 0, 60, 40.07, 40.41, 1, 2, 59, 74.38, 55.99, 0, 60, 31.22, 38.51, 1, 2, 59, 74.42, 54.31, 4e-05, 60, 30.27, 37.13, 0.99996, 1, 60, 31.17, 34.25, 1, 1, 60, 34.34, 32.25, 1, 2, 59, 60.53, 42.5, 0.04001, 60, 12.09, 35.75, 0.95999, 2, 59, 62.09, 40.78, 0.03944, 60, 12.34, 33.43, 0.96056, 2, 59, 62.96, 38.8, 0.04261, 60, 11.88, 31.32, 0.95739, 2, 59, 64.76, 36.2, 0.04281, 60, 11.81, 28.16, 0.95719, 2, 59, 70.01, 28.85, 0.02882, 60, 11.73, 19.13, 0.97118], "width": 129, "edges": [0, 2, 2, 4, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 64, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 88, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 108, 128, 130, 130, 132, 132, 134, 134, 136, 4, 6, 136, 6, 6, 8, 8, 10, 8, 138, 138, 140, 140, 142, 142, 144, 144, 128, 146, 148, 148, 150, 150, 152, 152, 154], "type": "mesh", "hull": 32, "height": 144}}, "free4": {"free4": {"triangles": [12, 11, 0, 12, 0, 13, 11, 1, 0, 11, 2, 1, 11, 3, 2, 10, 3, 11, 4, 3, 10, 9, 4, 10, 8, 5, 4, 9, 8, 4, 7, 6, 5, 8, 7, 5], "uvs": [0.84585, 0.00025, 0.66876, 0.03775, 0.48387, 0.13525, 0.3771, 0.2665, 0.3172, 0.46025, 0.22085, 0.694, 0, 0.90525, 0.12189, 1, 0.47085, 0.97275, 0.79116, 0.8015, 0.93179, 0.5415, 0.95262, 0.309, 0.98647, 0.119, 0.95783, 0], "vertices": [1, 12, 86.92, 38.99, 1, 1, 12, 80.83, 46.01, 1, 1, 12, 68.89, 51.61, 1, 1, 12, 54.84, 52.63, 1, 1, 12, 35.48, 49.66, 1, 1, 12, 11.78, 47.18, 1, 1, 12, -11.53, 51.07, 1, 1, 12, -18.86, 42.68, 1, 1, 12, -11.31, 27.48, 1, 1, 12, 9.59, 17.85, 1, 1, 12, 36.43, 19.07, 1, 1, 12, 58.93, 24.98, 1, 1, 12, 77.57, 29.04, 1, 1, 12, 88.53, 33.86, 1], "width": 48, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "type": "mesh", "hull": 14, "height": 100}}, "free5": {"free5": {"triangles": [1, 21, 0, 2, 20, 21, 1, 2, 21, 20, 2, 3, 3, 4, 20, 4, 19, 20, 4, 6, 19, 5, 6, 4, 18, 19, 6, 18, 6, 7, 15, 13, 14, 12, 13, 15, 8, 18, 7, 9, 18, 8, 11, 16, 17, 10, 11, 17, 18, 10, 17, 12, 15, 16, 11, 12, 16, 9, 10, 18], "uvs": [0.83198, 0, 0.88647, 0, 0.99909, 0.1683, 1, 0.31017, 0.96034, 0.38373, 0.94944, 0.53084, 0.9119, 0.56894, 0.95065, 0.75546, 0.82229, 0.89864, 0.70241, 0.97745, 0.47838, 1, 0.35608, 1, 0.10178, 0.88551, 0, 0.68979, 0.00854, 0.65695, 0.17444, 0.55974, 0.3476, 0.55712, 0.55467, 0.64118, 0.57889, 0.65826, 0.67819, 0.44152, 0.77264, 0.27207, 0.8126, 0.11576], "vertices": [44.09, 49.35, 51.07, 49.35, 65.48, 29.49, 65.6, 12.75, 60.52, 4.07, 59.13, -13.29, 54.32, -17.79, 59.28, -39.8, 42.85, -56.69, 27.51, -65.99, -1.17, -68.65, -16.82, -68.65, -49.37, -55.14, -62.4, -32.05, -61.31, -28.17, -40.07, -16.7, -17.91, -16.39, 8.6, -26.31, 11.7, -28.33, 24.41, -2.75, 36.5, 17.24, 41.61, 35.69], "width": 128, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "type": "mesh", "hull": 22, "height": 118}}, "batgioiitem2": {"batgioiitem2": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [71.48, -70.25, -70.52, -70.25, -70.52, 71.75, 71.48, 71.75], "width": 142, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 142}}, "jackpot14": {"jackpot3": {"triangles": [0, 15, 1, 1, 15, 14, 1, 14, 2, 2, 14, 13, 2, 13, 3, 3, 13, 12, 3, 12, 4, 4, 12, 11, 4, 11, 5, 5, 11, 10, 5, 10, 6, 6, 10, 9, 6, 9, 7, 7, 9, 8], "uvs": [1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0], "vertices": [1, 41, 35.09, -28.74, 1, 2, 40, 46.17, -28.74, 0.06837, 41, 5.66, -28.74, 0.93163, 3, 39, 66.87, -28.74, 0.0065, 40, 16.74, -28.74, 0.59444, 41, -23.76, -28.74, 0.39905, 3, 39, 37.44, -28.74, 0.27298, 40, -12.69, -28.74, 0.71052, 41, -53.19, -28.74, 0.0165, 3, 38, 52.83, -28.74, 0.03418, 39, 8.01, -28.74, 0.76971, 40, -42.12, -28.74, 0.19611, 3, 38, 23.41, -28.74, 0.4606, 39, -21.41, -28.74, 0.53864, 40, -71.55, -28.74, 0.00077, 2, 38, -6.02, -28.74, 0.94674, 39, -50.84, -28.74, 0.05326, 1, 38, -35.45, -28.74, 1, 1, 38, -35.45, 23.26, 1, 2, 38, -6.02, 23.26, 0.96894, 39, -50.84, 23.26, 0.03106, 3, 38, 23.41, 23.26, 0.4519, 39, -21.41, 23.26, 0.54805, 40, -71.55, 23.26, 5e-05, 3, 38, 52.83, 23.26, 0.01617, 39, 8.01, 23.26, 0.80374, 40, -42.12, 23.26, 0.1801, 3, 39, 37.44, 23.26, 0.24985, 40, -12.69, 23.26, 0.74661, 41, -53.19, 23.26, 0.00354, 3, 39, 66.87, 23.26, 0.00107, 40, 16.74, 23.26, 0.6171, 41, -23.76, 23.26, 0.38183, 2, 40, 46.17, 23.26, 0.04591, 41, 5.66, 23.26, 0.95409, 1, 41, 35.09, 23.26, 1], "width": 206, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0], "type": "mesh", "hull": 16, "height": 52}}, "batgioiitem3": {"batgioiitem3": {"triangles": [4, 13, 3, 4, 14, 13, 15, 14, 4, 12, 3, 13, 11, 12, 13, 14, 7, 13, 13, 10, 11, 14, 6, 7, 10, 13, 9, 7, 8, 13, 8, 9, 13, 15, 4, 5, 0, 15, 5, 14, 15, 0, 6, 14, 0, 2, 3, 12, 2, 12, 11, 7, 6, 0, 0, 8, 7, 1, 8, 0, 9, 8, 1, 2, 10, 9, 2, 11, 10, 9, 1, 2], "uvs": [1, 0.66509, 1, 1, 0, 1, 0, 0, 0.94112, 0, 1, 0, 0.80427, 0.51543, 0.75942, 0.52563, 0.67432, 0.53697, 0.56392, 0.54264, 0.42017, 0.5279, 0.32012, 0.48935, 0.25687, 0.41339, 0.41327, 0.21951, 0.84337, 0.23651, 0.93652, 0.13107], "vertices": [1, 1, 49.14, -68.69, 1, 1, 1, 1.61, -70.28, 1, 1, 1, -3.05, 69.65, 1, 1, 1, 138.87, 74.38, 1, 1, 1, 143.26, -57.31, 1, 1, 1, 143.53, -65.55, 1, 1, 1, 69.47, -40.6, 1, 1, 1, 67.81, -34.37, 1, 1, 1, 65.81, -22.52, 1, 1, 1, 64.49, -7.1, 1, 1, 1, 65.91, 13.09, 1, 1, 1, 70.91, 27.27, 1, 1, 1, 81.4, 36.48, 1, 1, 1, 109.64, 15.51, 1, 1, 1, 109.24, -44.75, 1, 1, 1, 124.64, -57.28, 1], "width": 140, "edges": [2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 6, 8, 8, 10, 30, 8, 2, 0, 0, 10, 12, 0], "type": "mesh", "hull": 6, "height": 142}}, "jackpot13": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [15.39, -15.86, -15.61, -15.86, -15.61, 16.14, 15.39, 16.14], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "free8": {"free8": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 12, 40.61, -39.45, 1, 1, 12, 25.56, 9.28, 1, 1, 12, 49.45, 16.66, 1, 1, 12, 64.5, -32.07, 1], "width": 51, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 25}}, "batgioiitem1": {"batgioiitem1": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [75, -74, -74, -74, -74, 75, 75, 75], "width": 149, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 149}}, "free9": {"free9": {"triangles": [8, 10, 7, 8, 9, 10, 7, 10, 11, 7, 11, 6, 6, 12, 5, 6, 11, 12, 5, 12, 4, 12, 13, 4, 13, 14, 4, 3, 14, 15, 3, 4, 14, 3, 15, 0, 3, 0, 2, 0, 1, 2], "uvs": [0.92608, 0, 1, 0.04364, 1, 0.07177, 0.8859, 0.11708, 0.77875, 0.24833, 0.72295, 0.43271, 0.60465, 0.64833, 0.435, 0.84052, 0.1292, 1, 0, 1, 0.25867, 0.85146, 0.48188, 0.64364, 0.61134, 0.39833, 0.6359, 0.21239, 0.6917, 0.04833, 0.82117, 0], "vertices": [2, 13, 2.6, -2.87, 0.98814, 14, -3.58, -1.07, 0.01186, 2, 13, -1.11, 1.07, 0.99602, 14, -4.5, 4.26, 0.00398, 2, 13, -0.85, 3.31, 0.96562, 14, -3.06, 5.98, 0.03438, 3, 13, 5.91, 6.17, 0.12182, 14, 4.17, 4.65, 0.70097, 15, -2.75, 7.8, 0.17722, 1, 15, 8.79, 4.17, 1, 2, 15, 23.87, 4.27, 0.11436, 16, 1.81, 4.4, 0.88564, 1, 16, 20.25, 3.19, 1, 1, 17, 16.44, 4.79, 1, 1, 17, 36.97, -1.1, 1, 1, 17, 41.36, -6.86, 1, 1, 17, 23.13, -2.54, 1, 1, 17, 2.33, -2.67, 1, 1, 16, 1.03, -2.39, 1, 1, 15, 7.69, -4.25, 1, 1, 14, 8.95, -6.56, 1, 2, 13, 8.44, -3.55, 0.04417, 14, 0.91, -4.86, 0.95583], "width": 56, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "type": "mesh", "hull": 16, "height": 80}}, "fire100": {"fire102": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire101": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire104": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire103": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire100": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire111": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire110": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire109": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire106": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire105": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire108": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}, "fire107": {"rotation": -90, "width": 225, "y": -0.5, "height": 210}}, "Monkeyking11": {"Monkeyking11": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 50, 30.07, 3.64, 1, 1, 50, 26.56, 24.35, 1, 1, 50, 45.3, 27.52, 1, 1, 50, 48.8, 6.81, 1], "width": 21, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 19}}, "Monkeyking10": {"Monkeyking10": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 50, 31.74, -28.29, 1, 1, 50, 27.24, -1.67, 1, 1, 50, 37.09, 0, 1, 1, 50, 41.6, -26.62, 1], "width": 27, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 10}}, "jackpot1": {"jackpot1": {"triangles": [12, 13, 14, 11, 15, 10, 14, 11, 12, 15, 11, 14, 7, 8, 9, 10, 16, 9, 16, 10, 15, 5, 6, 9, 7, 9, 6, 9, 17, 5, 17, 9, 16, 4, 17, 18, 4, 18, 19, 3, 4, 19, 20, 3, 19, 5, 17, 4, 0, 3, 20, 2, 3, 0, 1, 2, 0], "uvs": [0.99824, 0.33407, 0.98287, 1, 0.8445, 0.9295, 0.6969, 0.87359, 0.53085, 0.83725, 0.36172, 0.81768, 0.21105, 0.78833, 0.07575, 0.73661, 0, 0.65555, 0.08805, 0.40116, 0.11573, 0.26139, 0.10343, 0.08668, 0.18645, 0.02657, 0.2787, 0, 0.28177, 0.06152, 0.30637, 0.10624, 0.37402, 0.1356, 0.51855, 0.15936, 0.6723, 0.18032, 0.7953, 0.21806, 0.8937, 0.26558], "vertices": [-2.96, 35.59, -3.58, -23.01, -9.11, -16.81, -15.01, -11.89, -21.66, -8.69, -28.42, -6.97, -34.45, -4.39, -39.86, 0.16, -42.89, 7.3, -39.37, 29.68, -38.26, 41.98, -38.75, 57.36, -35.43, 62.65, -31.74, 64.99, -31.62, 59.57, -30.64, 55.64, -27.93, 53.05, -22.15, 50.96, -16, 49.12, -11.08, 45.8, -7.14, 41.62], "width": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "type": "mesh", "hull": 21, "height": 88}}, "jackpot3": {"jackpot3": {"triangles": [0, 15, 1, 1, 15, 14, 1, 14, 2, 2, 14, 13, 2, 13, 3, 3, 13, 12, 3, 12, 4, 4, 12, 11, 4, 11, 5, 5, 11, 10, 5, 10, 6, 6, 10, 9, 6, 9, 7, 7, 9, 8], "uvs": [1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0], "vertices": [1, 41, 35.09, -28.74, 1, 2, 40, 46.17, -28.74, 0.06837, 41, 5.66, -28.74, 0.93163, 3, 39, 66.87, -28.74, 0.0065, 40, 16.74, -28.74, 0.59444, 41, -23.76, -28.74, 0.39905, 3, 39, 37.44, -28.74, 0.27298, 40, -12.69, -28.74, 0.71052, 41, -53.19, -28.74, 0.0165, 3, 38, 52.83, -28.74, 0.03418, 39, 8.01, -28.74, 0.76971, 40, -42.12, -28.74, 0.19611, 3, 38, 23.41, -28.74, 0.4606, 39, -21.41, -28.74, 0.53864, 40, -71.55, -28.74, 0.00077, 2, 38, -6.02, -28.74, 0.94674, 39, -50.84, -28.74, 0.05326, 1, 38, -35.45, -28.74, 1, 1, 38, -35.45, 23.26, 1, 2, 38, -6.02, 23.26, 0.96894, 39, -50.84, 23.26, 0.03106, 3, 38, 23.41, 23.26, 0.4519, 39, -21.41, 23.26, 0.54805, 40, -71.55, 23.26, 5e-05, 3, 38, 52.83, 23.26, 0.01617, 39, 8.01, 23.26, 0.80374, 40, -42.12, 23.26, 0.1801, 3, 39, 37.44, 23.26, 0.24985, 40, -12.69, 23.26, 0.74661, 41, -53.19, 23.26, 0.00354, 3, 39, 66.87, 23.26, 0.00107, 40, 16.74, 23.26, 0.6171, 41, -23.76, 23.26, 0.38183, 2, 40, 46.17, 23.26, 0.04591, 41, 5.66, 23.26, 0.95409, 1, 41, 35.09, 23.26, 1], "width": 206, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0], "type": "mesh", "hull": 16, "height": 52}}, "jackpot2": {"jackpot2": {"triangles": [0, 3, 2, 0, 2, 1, 6, 5, 4, 4, 3, 6, 0, 6, 3, 8, 7, 0, 6, 0, 7, 13, 0, 14, 0, 10, 8, 0, 12, 11, 12, 0, 13, 10, 0, 11, 9, 8, 10], "uvs": [0.50466, 0.13242, 0.45917, 0.05603, 0.39853, 0.0382, 0.14971, 0.12307, 0.07003, 0.15025, 0, 1, 0.47265, 1, 0.50803, 1, 0.53835, 1, 1, 1, 0.92413, 0.12478, 0.8618, 0.05348, 0.64448, 0, 0.5771, 1e-05, 0.5215, 0.03056], "vertices": [-3.21, 36.33, -9.13, 42.9, -17.01, 44.43, -49.55, 41.4, -59.72, 34.8, -68.82, -38.28, -7.38, -38.28, -2.78, -38.28, 1.17, -38.28, 61.18, -38.28, 51.32, 36.99, 43.21, 43.12, 14.96, 47.72, 6.2, 47.72, -1.02, 45.09], "width": 130, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 4, 6, 6, 8], "type": "mesh", "hull": 15, "height": 86}}, "jackpot5": {"jackpot5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [15.39, -15.86, -15.61, -15.86, -15.61, 16.14, 15.39, 16.14], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "jackpot4": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-21.97, -7.01, -21.97, 6.99, 27.03, 6.99, 27.03, -7.01], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "jackpot7": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-21.97, -7.01, -21.97, 6.99, 27.03, 6.99, 27.03, -7.01], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "wild11": {"wild11": {"triangles": [5, 8, 6, 8, 5, 4, 2, 1, 9, 2, 4, 3, 8, 4, 2, 8, 7, 6, 8, 2, 9, 1, 10, 9, 1, 0, 10], "uvs": [0, 0, 0.00587, 0.27852, 0.09454, 0.62352, 0.36754, 1, 0.64987, 1, 0.90187, 0.81252, 1, 0.57552, 1, 0.39252, 0.7502, 0.20952, 0.3232, 0.10452, 0.0502, 0], "vertices": [1, 52, 0.22, -18.05, 1, 1, 53, -3.11, -7.85, 1, 1, 53, 7.04, -7.23, 1, 2, 53, 19.74, -0.42, 0.68475, 54, 11.47, -14.42, 0.31525, 2, 53, 22.34, 9.4, 0.20374, 54, 15.37, -5.04, 0.79626, 2, 53, 19.59, 19.52, 0.00072, 54, 14, 5.36, 0.99928, 1, 54, 9.23, 11.16, 1, 1, 52, 2.15, 19.54, 1, 1, 52, -0.64, 9.57, 1, 1, 52, 0.24, -6.06, 1, 1, 52, -0.22, -16.3, 1], "width": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 28}}, "jackpot6": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-21.97, -7.01, -21.97, 6.99, 27.03, 6.99, 27.03, -7.01], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "jackpot9": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-21.97, -7.01, -21.97, 6.99, 27.03, 6.99, 27.03, -7.01], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "jackpot8": {"jackpot4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-21.97, -7.01, -21.97, 6.99, 27.03, 6.99, 27.03, -7.01], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "wild10": {"wild10": {"triangles": [106, 107, 4, 38, 107, 106, 106, 105, 32, 105, 106, 0, 101, 3, 4, 100, 3, 101, 108, 101, 4, 41, 5, 101, 100, 101, 5, 99, 3, 100, 6, 99, 100, 107, 108, 4, 5, 6, 100, 108, 41, 101, 7, 6, 5, 7, 5, 41, 40, 41, 108, 8, 99, 6, 8, 6, 7, 39, 40, 108, 38, 39, 108, 107, 38, 108, 11, 99, 8, 77, 8, 7, 76, 7, 41, 77, 7, 76, 10, 11, 8, 98, 99, 11, 12, 98, 11, 37, 39, 38, 78, 8, 77, 9, 10, 8, 75, 76, 41, 99, 98, 3, 43, 77, 76, 44, 43, 76, 42, 78, 77, 43, 42, 77, 78, 9, 8, 75, 44, 76, 4, 0, 106, 36, 37, 38, 106, 36, 38, 52, 42, 43, 45, 44, 75, 13, 98, 12, 40, 74, 75, 40, 75, 41, 45, 75, 74, 9, 12, 10, 79, 9, 78, 42, 79, 78, 39, 86, 40, 87, 39, 37, 35, 87, 37, 40, 85, 74, 46, 45, 74, 36, 35, 37, 39, 87, 86, 44, 51, 52, 44, 52, 43, 50, 51, 44, 88, 87, 35, 47, 46, 74, 74, 73, 47, 40, 86, 85, 85, 73, 74, 45, 50, 44, 45, 49, 50, 48, 46, 47, 45, 46, 49, 48, 49, 46, 56, 86, 87, 55, 85, 86, 56, 55, 86, 83, 47, 73, 48, 47, 83, 57, 56, 87, 84, 73, 85, 54, 85, 55, 84, 85, 54, 79, 51, 80, 52, 79, 42, 51, 79, 52, 53, 84, 54, 13, 12, 79, 10, 12, 11, 9, 79, 12, 15, 14, 79, 14, 13, 79, 58, 57, 87, 58, 87, 88, 33, 36, 106, 35, 36, 33, 84, 83, 73, 94, 84, 53, 94, 83, 84, 34, 35, 33, 88, 35, 34, 59, 58, 88, 65, 53, 54, 94, 53, 65, 80, 15, 79, 88, 89, 59, 34, 89, 88, 82, 49, 48, 82, 48, 83, 97, 98, 13, 97, 13, 14, 32, 33, 106, 50, 80, 51, 81, 50, 49, 81, 49, 82, 81, 80, 50, 16, 15, 80, 55, 65, 54, 64, 65, 55, 58, 61, 57, 60, 58, 59, 63, 55, 56, 64, 55, 63, 62, 63, 56, 56, 57, 62, 60, 61, 58, 61, 62, 57, 105, 31, 32, 33, 32, 31, 93, 94, 65, 93, 65, 64, 90, 59, 89, 60, 59, 90, 81, 17, 16, 81, 16, 80, 31, 34, 33, 30, 34, 31, 63, 93, 64, 92, 93, 63, 92, 63, 62, 91, 62, 61, 92, 62, 91, 61, 60, 90, 91, 61, 90, 34, 90, 89, 34, 30, 90, 29, 90, 30, 27, 91, 90, 28, 27, 90, 29, 28, 90, 66, 81, 82, 17, 81, 66, 16, 17, 15, 18, 17, 66, 68, 67, 66, 26, 92, 91, 26, 91, 27, 104, 29, 30, 30, 31, 105, 104, 30, 105, 68, 82, 69, 83, 93, 82, 94, 93, 83, 93, 69, 82, 92, 69, 93, 68, 66, 82, 15, 96, 97, 15, 97, 14, 17, 96, 15, 18, 96, 17, 92, 72, 69, 26, 72, 92, 70, 69, 72, 71, 70, 72, 67, 19, 18, 67, 18, 66, 25, 72, 26, 104, 103, 28, 104, 28, 29, 68, 20, 19, 68, 19, 67, 96, 2, 97, 26, 28, 25, 25, 103, 102, 27, 28, 26, 103, 25, 28, 24, 72, 25, 24, 25, 102, 71, 72, 24, 23, 70, 71, 23, 71, 24, 69, 21, 20, 69, 20, 68, 22, 21, 69, 70, 22, 69, 23, 22, 70, 98, 97, 2, 3, 98, 2, 95, 2, 96, 96, 19, 95, 19, 96, 18, 1, 19, 20, 1, 95, 19, 2, 95, 1, 104, 105, 0, 103, 104, 0, 102, 103, 0, 23, 24, 102, 0, 23, 102, 22, 23, 0, 1, 20, 21, 1, 21, 22, 1, 22, 0], "uvs": [1, 1, 0.17373, 1, 0, 1, 0, 0, 1, 0, 0.28119, 0.28366, 0.2498, 0.32338, 0.26662, 0.36778, 0.20719, 0.41218, 0.18926, 0.50916, 0.15001, 0.46009, 0.12086, 0.44139, 0.08274, 0.47761, 0.07265, 0.54772, 0.10741, 0.63067, 0.13095, 0.64703, 0.19486, 0.65755, 0.21953, 0.72064, 0.21168, 0.80594, 0.24644, 0.88071, 0.27907, 0.94052, 0.32162, 0.9815, 0.37156, 0.99289, 0.43427, 0.97926, 0.49871, 0.94965, 0.57046, 0.8924, 0.57943, 0.83164, 0.65792, 0.77673, 0.69604, 0.78841, 0.74537, 0.77439, 0.80143, 0.72064, 0.81825, 0.6926, 0.81825, 0.65521, 0.79919, 0.63535, 0.74425, 0.6447, 0.73977, 0.56174, 0.76667, 0.52902, 0.75658, 0.47878, 0.77452, 0.43088, 0.66465, 0.43088, 0.5458, 0.39466, 0.40677, 0.35026, 0.24104, 0.50584, 0.27837, 0.50407, 0.3051, 0.51689, 0.3314, 0.54077, 0.35983, 0.56066, 0.37764, 0.58056, 0.35898, 0.59824, 0.32631, 0.59957, 0.29067, 0.59161, 0.26098, 0.57393, 0.24274, 0.53988, 0.52904, 0.6302, 0.54145, 0.61906, 0.5688, 0.60612, 0.6038, 0.60541, 0.62931, 0.61317, 0.65202, 0.63216, 0.66509, 0.64913, 0.6517, 0.67143, 0.62891, 0.68205, 0.60463, 0.68507, 0.57894, 0.67986, 0.55369, 0.66705, 0.53712, 0.64568, 0.29167, 0.78891, 0.30256, 0.81728, 0.34117, 0.841, 0.38403, 0.8534, 0.4204, 0.87362, 0.45579, 0.87513, 0.47664, 0.86452, 0.39926, 0.58652, 0.37837, 0.54794, 0.34514, 0.49797, 0.29861, 0.45047, 0.24924, 0.44206, 0.21078, 0.48857, 0.18704, 0.5514, 0.22218, 0.62462, 0.28294, 0.65727, 0.33849, 0.65035, 0.39024, 0.61225, 0.50892, 0.6167, 0.53693, 0.58603, 0.58678, 0.56327, 0.65752, 0.55536, 0.71401, 0.5796, 0.72351, 0.64936, 0.70167, 0.70922, 0.62808, 0.75473, 0.55782, 0.7488, 0.51699, 0.69685, 0.50465, 0.64342, 0.09282, 0.94216, 0.09373, 0.84174, 0.02737, 0.65132, 0.01373, 0.47132, 0.031, 0.28374, 0.07919, 0.14353, 0.22191, 0.02511, 0.58858, 0.94345, 0.70252, 0.90556, 0.8007, 0.83608, 0.93525, 0.72745, 0.97404, 0.52156, 0.9607, 0.31061, 0.89161, 0.20198], "vertices": [1, 50, 1.61, -58.31, 1, 2, 50, -12.04, 22.34, 0.696, 55, -40, 26.52, 0.304, 1, 50, -14.91, 39.3, 1, 1, 50, 78.76, 55.15, 1, 1, 50, 95.28, -42.46, 1, 1, 50, 56.83, 23.21, 1, 1, 50, 52.59, 25.64, 1, 1, 50, 48.71, 23.3, 1, 1, 50, 43.57, 28.39, 1, 1, 50, 34.19, 28.61, 1, 1, 50, 38.14, 33.22, 1, 1, 50, 39.41, 36.36, 1, 1, 50, 35.39, 39.5, 1, 1, 50, 28.65, 39.38, 1, 1, 50, 21.46, 34.67, 1, 1, 50, 20.31, 32.11, 1, 1, 50, 20.39, 25.71, 1, 1, 50, 14.88, 22.3, 1, 1, 50, 6.76, 21.71, 1, 1, 50, 0.33, 17.13, 1, 1, 50, -4.73, 13, 1, 1, 50, -7.86, 8.2, 1, 1, 50, -8.11, 3.14, 1, 1, 50, -5.79, -2.76, 1, 1, 50, -1.95, -8.58, 1, 1, 50, 4.59, -14.68, 1, 1, 50, 10.43, -14.59, 1, 1, 50, 16.87, -21.38, 1, 1, 50, 16.41, -25.29, 1, 1, 50, 18.54, -29.88, 1, 1, 50, 24.5, -34.5, 1, 1, 50, 27.4, -35.7, 1, 1, 50, 30.91, -35.11, 1, 1, 50, 32.45, -32.93, 1, 1, 50, 30.67, -27.72, 1, 1, 50, 38.36, -25.96, 1, 1, 50, 41.87, -28.07, 1, 1, 50, 46.41, -26.29, 1, 1, 50, 51.2, -27.28, 1, 1, 50, 49.38, -16.55, 1, 1, 50, 50.81, -4.38, 1, 1, 50, 52.67, 9.9, 1, 1, 50, 35.36, 23.61, 1, 1, 50, 36.14, 19.99, 1, 1, 50, 35.38, 17.18, 1, 1, 50, 33.58, 14.23, 1, 1, 50, 32.19, 11.14, 1, 1, 50, 30.62, 9.09, 1, 1, 50, 28.65, 10.63, 1, 1, 50, 27.99, 13.8, 1, 1, 50, 28.15, 17.4, 1, 1, 50, 29.31, 20.58, 1, 1, 50, 32.2, 22.9, 1, 1, 50, 28.47, -6.48, 1, 1, 50, 29.72, -7.51, 1, 1, 50, 31.38, -9.98, 1, 1, 50, 32.03, -13.38, 1, 1, 50, 31.72, -16, 1, 1, 50, 30.32, -18.51, 1, 1, 50, 28.94, -20.06, 1, 1, 50, 26.63, -19.11, 1, 1, 50, 25.26, -17.05, 1, 1, 50, 24.58, -14.73, 1, 1, 50, 24.64, -12.14, 1, 1, 50, 25.43, -9.47, 1, 1, 50, 27.15, -7.51, 1, 1, 50, 9.68, 14.18, 1, 1, 50, 7.2, 12.66, 1, 1, 50, 5.62, 8.52, 1, 1, 50, 5.17, 4.14, 1, 1, 50, 3.87, 0.27, 1, 1, 50, 4.32, -3.21, 1, 1, 50, 5.66, -5.08, 1, 1, 50, 30.42, 6.88, 1, 1, 50, 33.69, 9.53, 1, 1, 50, 37.82, 13.57, 1, 1, 50, 41.5, 18.86, 1, 1, 50, 41.47, 23.82, 1, 1, 50, 36.48, 26.83, 1, 1, 50, 30.2, 28.15, 1, 1, 50, 23.92, 23.56, 1, 1, 50, 21.87, 17.11, 1, 1, 50, 23.43, 11.8, 1, 1, 50, 27.86, 7.36, 1, 1, 50, 29.4, -4.3, 1, 1, 50, 32.74, -6.55, 1, 1, 50, 35.69, -11.05, 1, 1, 50, 37.6, -17.83, 1, 1, 50, 36.27, -23.73, 1, 1, 50, 29.89, -25.76, 1, 1, 50, 23.92, -24.58, 1, 1, 50, 18.44, -18.12, 1, 1, 50, 17.84, -11.17, 1, 1, 50, 22.03, -6.36, 1, 1, 50, 26.83, -4.31, 1, 2, 50, -7.96, 31.16, 0.504, 55, -30.35, 27.62, 0.496, 2, 50, 1.46, 32.66, 0.384, 55, -24.17, 20.36, 0.616, 2, 50, 18.2, 42.16, 0.496, 55, -7.34, 11.01, 0.504, 2, 50, 34.84, 46.34, 0.16, 55, 4.89, -1.02, 0.84, 2, 50, 52.69, 47.63, 0.416, 55, 15.28, -15.59, 0.584, 2, 50, 66.62, 45.15, 0.6, 55, 20.41, -28.78, 0.4, 1, 50, 80.07, 33.09, 1, 2, 50, 0.11, -17.26, 0.768, 56, -25.76, -24.04, 0.232, 2, 50, 5.54, -27.78, 0.576, 56, -14.38, -20.76, 0.424, 2, 50, 13.67, -36.26, 0.352, 56, -4.48, -14.45, 0.648, 2, 50, 26.07, -47.67, 0.184, 56, 9.14, -4.52, 0.816, 2, 50, 46, -48.19, 0.256, 56, 13.54, 14.92, 0.744, 2, 50, 65.54, -43.55, 0.168, 56, 12.8, 34.99, 0.832, 1, 50, 74.57, -35.08, 1], "width": 99, "edges": [4, 6, 6, 8, 0, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 10, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 84, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 106, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 146, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 168, 0, 2, 2, 4, 2, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216], "type": "mesh", "hull": 5, "height": 95}}, "wild13": {"wild13": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 57, -22.21, -62.61, 1, 1, 57, -22.21, 61.39, 1, 1, 57, 23.79, 61.39, 1, 1, 57, 23.79, -62.61, 1], "width": 124, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 46}}, "wild14": {"wild13": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 58, -22.21, -62.61, 1, 1, 58, -22.21, 61.39, 1, 1, 58, 23.79, 61.39, 1, 1, 58, 23.79, -62.61, 1], "width": 124, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 46}}, "bonus7": {"bonus1": {"triangles": [20, 17, 21, 1, 20, 2, 20, 18, 17, 19, 18, 20, 1, 19, 20, 0, 19, 1, 22, 16, 15, 21, 16, 22, 3, 21, 22, 2, 21, 3, 21, 17, 16, 2, 20, 21, 24, 14, 13, 23, 14, 24, 5, 23, 24, 4, 23, 5, 23, 15, 14, 22, 15, 23, 4, 22, 23, 3, 22, 4, 6, 25, 7, 25, 13, 12, 24, 13, 25, 6, 24, 25, 5, 24, 6, 9, 11, 10, 26, 11, 9, 8, 26, 9, 7, 26, 8, 26, 12, 11, 25, 12, 26, 7, 25, 26], "uvs": [1, 1, 0.875, 1, 0.75, 1, 0.625, 1, 0.5, 1, 0.375, 1, 0.25, 1, 0.125, 1, 0, 1, 0, 0.5, 0, 0, 0.125, 0, 0.25, 0, 0.375, 0, 0.5, 0, 0.625, 0, 0.75, 0, 0.875, 0, 1, 0, 1, 0.5, 0.875, 0.5, 0.75, 0.5, 0.625, 0.5, 0.5, 0.5, 0.375, 0.5, 0.25, 0.5, 0.125, 0.5], "vertices": [2, 47, 49.34, -29.79, 0.01947, 48, 17.4, -29.79, 0.98053, 3, 46, 62.99, -29.79, 0.00033, 47, 28.21, -29.79, 0.22829, 48, -3.72, -29.79, 0.77138, 3, 46, 41.86, -29.79, 0.06967, 47, 7.09, -29.79, 0.6482, 48, -24.85, -29.79, 0.28213, 4, 45, 54.85, -29.79, 0.00712, 46, 20.74, -29.79, 0.4132, 47, -14.04, -29.79, 0.55634, 48, -45.97, -29.79, 0.02334, 3, 45, 33.73, -29.79, 0.16552, 46, -0.39, -29.79, 0.68406, 47, -35.16, -29.79, 0.15042, 4, 44, 42.79, -29.79, 0.03177, 45, 12.6, -29.79, 0.57768, 46, -21.51, -29.79, 0.38474, 47, -56.29, -29.79, 0.0058, 3, 44, 21.67, -29.79, 0.331, 45, -8.52, -29.79, 0.61409, 46, -42.64, -29.79, 0.05491, 3, 44, 0.54, -29.79, 0.81784, 45, -29.65, -29.79, 0.18212, 46, -63.76, -29.79, 3e-05, 2, 44, -20.58, -29.79, 0.99261, 45, -50.77, -29.79, 0.00739, 1, 44, -20.58, -3.79, 1, 1, 44, -20.58, 22.21, 1, 2, 44, 0.54, 22.21, 0.86478, 45, -29.65, 22.21, 0.13522, 3, 44, 21.67, 22.21, 0.29103, 45, -8.52, 22.21, 0.67867, 46, -42.64, 22.21, 0.0303, 3, 44, 42.79, 22.21, 0.00713, 45, 12.6, 22.21, 0.61319, 46, -21.51, 22.21, 0.37968, 3, 45, 33.73, 22.21, 0.12051, 46, -0.39, 22.21, 0.7802, 47, -35.16, 22.21, 0.09929, 4, 45, 54.85, 22.21, 9e-05, 46, 20.74, 22.21, 0.41146, 47, -14.04, 22.21, 0.58622, 48, -45.97, 22.21, 0.00223, 3, 46, 41.86, 22.21, 0.03318, 47, 7.09, 22.21, 0.74611, 48, -24.85, 22.21, 0.2207, 2, 47, 28.21, 22.21, 0.20596, 48, -3.72, 22.21, 0.79404, 2, 47, 49.34, 22.21, 0.00031, 48, 17.4, 22.21, 0.99969, 1, 48, 17.4, -3.79, 1, 2, 47, 28.21, -3.79, 0.10609, 48, -3.72, -3.79, 0.89391, 3, 46, 41.86, -3.79, 0.00147, 47, 7.09, -3.79, 0.8159, 48, -24.85, -3.79, 0.18263, 2, 46, 20.74, -3.79, 0.38931, 47, -14.04, -3.79, 0.61069, 3, 45, 33.73, -3.79, 0.03435, 46, -0.39, -3.79, 0.94134, 47, -35.16, -3.79, 0.02431, 2, 45, 12.6, -3.79, 0.65166, 46, -21.51, -3.79, 0.34834, 2, 44, 21.67, -3.79, 0.25191, 45, -8.52, -3.79, 0.74809, 2, 44, 0.54, -3.79, 0.96283, 45, -29.65, -3.79, 0.03717], "width": 169, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0], "type": "mesh", "hull": 20, "height": 52}}, "bonus1": {"bonus1": {"triangles": [20, 17, 21, 1, 20, 2, 20, 18, 17, 19, 18, 20, 1, 19, 20, 0, 19, 1, 22, 16, 15, 21, 16, 22, 3, 21, 22, 2, 21, 3, 21, 17, 16, 2, 20, 21, 24, 14, 13, 23, 14, 24, 5, 23, 24, 4, 23, 5, 23, 15, 14, 22, 15, 23, 4, 22, 23, 3, 22, 4, 6, 25, 7, 25, 13, 12, 24, 13, 25, 6, 24, 25, 5, 24, 6, 9, 11, 10, 26, 11, 9, 8, 26, 9, 7, 26, 8, 26, 12, 11, 25, 12, 26, 7, 25, 26], "uvs": [1, 1, 0.875, 1, 0.75, 1, 0.625, 1, 0.5, 1, 0.375, 1, 0.25, 1, 0.125, 1, 0, 1, 0, 0.5, 0, 0, 0.125, 0, 0.25, 0, 0.375, 0, 0.5, 0, 0.625, 0, 0.75, 0, 0.875, 0, 1, 0, 1, 0.5, 0.875, 0.5, 0.75, 0.5, 0.625, 0.5, 0.5, 0.5, 0.375, 0.5, 0.25, 0.5, 0.125, 0.5], "vertices": [2, 47, 49.34, -29.79, 0.01947, 48, 17.4, -29.79, 0.98053, 3, 46, 62.99, -29.79, 0.00033, 47, 28.21, -29.79, 0.22829, 48, -3.72, -29.79, 0.77138, 3, 46, 41.86, -29.79, 0.06967, 47, 7.09, -29.79, 0.6482, 48, -24.85, -29.79, 0.28213, 4, 45, 54.85, -29.79, 0.00712, 46, 20.74, -29.79, 0.4132, 47, -14.04, -29.79, 0.55634, 48, -45.97, -29.79, 0.02334, 3, 45, 33.73, -29.79, 0.16552, 46, -0.39, -29.79, 0.68406, 47, -35.16, -29.79, 0.15042, 4, 44, 42.79, -29.79, 0.03177, 45, 12.6, -29.79, 0.57768, 46, -21.51, -29.79, 0.38474, 47, -56.29, -29.79, 0.0058, 3, 44, 21.67, -29.79, 0.331, 45, -8.52, -29.79, 0.61409, 46, -42.64, -29.79, 0.05491, 3, 44, 0.54, -29.79, 0.81784, 45, -29.65, -29.79, 0.18212, 46, -63.76, -29.79, 3e-05, 2, 44, -20.58, -29.79, 0.99261, 45, -50.77, -29.79, 0.00739, 1, 44, -20.58, -3.79, 1, 1, 44, -20.58, 22.21, 1, 2, 44, 0.54, 22.21, 0.86478, 45, -29.65, 22.21, 0.13522, 3, 44, 21.67, 22.21, 0.29103, 45, -8.52, 22.21, 0.67867, 46, -42.64, 22.21, 0.0303, 3, 44, 42.79, 22.21, 0.00713, 45, 12.6, 22.21, 0.61319, 46, -21.51, 22.21, 0.37968, 3, 45, 33.73, 22.21, 0.12051, 46, -0.39, 22.21, 0.7802, 47, -35.16, 22.21, 0.09929, 4, 45, 54.85, 22.21, 9e-05, 46, 20.74, 22.21, 0.41146, 47, -14.04, 22.21, 0.58622, 48, -45.97, 22.21, 0.00223, 3, 46, 41.86, 22.21, 0.03318, 47, 7.09, 22.21, 0.74611, 48, -24.85, 22.21, 0.2207, 2, 47, 28.21, 22.21, 0.20596, 48, -3.72, 22.21, 0.79404, 2, 47, 49.34, 22.21, 0.00031, 48, 17.4, 22.21, 0.99969, 1, 48, 17.4, -3.79, 1, 2, 47, 28.21, -3.79, 0.10609, 48, -3.72, -3.79, 0.89391, 3, 46, 41.86, -3.79, 0.00147, 47, 7.09, -3.79, 0.8159, 48, -24.85, -3.79, 0.18263, 2, 46, 20.74, -3.79, 0.38931, 47, -14.04, -3.79, 0.61069, 3, 45, 33.73, -3.79, 0.03435, 46, -0.39, -3.79, 0.94134, 47, -35.16, -3.79, 0.02431, 2, 45, 12.6, -3.79, 0.65166, 46, -21.51, -3.79, 0.34834, 2, 44, 21.67, -3.79, 0.25191, 45, -8.52, -3.79, 0.74809, 2, 44, 0.54, -3.79, 0.96283, 45, -29.65, -3.79, 0.03717], "width": 169, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0], "type": "mesh", "hull": 20, "height": 52}}, "bonus2": {"bonus2": {"triangles": [17, 15, 14, 16, 15, 17, 49, 22, 21, 23, 22, 49, 18, 17, 14, 18, 14, 13, 24, 23, 49, 50, 18, 13, 50, 13, 12, 19, 18, 50, 20, 19, 50, 49, 21, 20, 50, 49, 20, 11, 51, 50, 54, 49, 50, 54, 50, 51, 12, 11, 50, 52, 54, 51, 55, 49, 54, 55, 54, 52, 53, 55, 52, 10, 51, 11, 52, 51, 10, 53, 52, 10, 56, 55, 53, 34, 56, 53, 34, 53, 10, 57, 56, 34, 49, 38, 39, 35, 55, 56, 35, 56, 57, 49, 55, 38, 25, 24, 49, 39, 25, 49, 25, 41, 26, 38, 55, 37, 35, 36, 55, 36, 37, 55, 39, 40, 25, 40, 41, 25, 48, 40, 39, 42, 26, 41, 47, 46, 48, 47, 39, 38, 47, 48, 39, 45, 41, 40, 45, 40, 48, 45, 48, 46, 44, 42, 41, 45, 44, 41, 43, 26, 42, 43, 42, 44, 27, 26, 43, 29, 45, 46, 29, 46, 47, 28, 43, 44, 27, 43, 28, 29, 28, 44, 29, 44, 45, 30, 47, 38, 29, 47, 30, 30, 38, 37, 36, 31, 30, 6, 5, 4, 3, 6, 4, 1, 33, 32, 0, 33, 1, 37, 36, 30, 31, 57, 2, 32, 31, 2, 1, 32, 2, 34, 10, 9, 34, 9, 8, 7, 57, 34, 57, 31, 35, 35, 31, 36, 57, 7, 2, 8, 7, 34, 2, 7, 6, 2, 6, 3], "uvs": [0.15199, 0.84842, 0.20421, 0.96515, 0.35813, 1, 0.93118, 0.9478, 1, 0.86577, 1, 0.76954, 0.87663, 0.69092, 0.81497, 0.54422, 0.74272, 0.45513, 0.66374, 0.39306, 0.5519, 0.37991, 0.60029, 0.31498, 0.60834, 0.21875, 0.60485, 0.13095, 0.3449, 1e-05, 0.2798, 0.01078, 0.24083, 0.05374, 0.30217, 0.11101, 0.30474, 0.12101, 0.30192, 0.12918, 0.29713, 0.13101, 0.19668, 0.07219, 0.16495, 0.08093, 0.13956, 0.10478, 0.09835, 0.14349, 0.0326, 0.27158, 0.00406, 0.43144, 0.0153, 0.51684, 0.06375, 0.55258, 0.15371, 0.52875, 0.21426, 0.57244, 0.26357, 0.71444, 0.25924, 0.78593, 0.16928, 0.82168, 0.49929, 0.40701, 0.43782, 0.427, 0.38249, 0.44229, 0.32204, 0.44582, 0.26672, 0.44229, 0.18885, 0.43876, 0.12541, 0.44589, 0.10946, 0.45074, 0.07642, 0.47896, 0.0446, 0.49227, 0.08502, 0.49165, 0.1213, 0.48805, 0.15892, 0.48291, 0.19071, 0.48496, 0.1513, 0.45669, 0.18824, 0.11547, 0.51249, 0.29089, 0.51546, 0.30498, 0.50572, 0.34482, 0.50064, 0.37835, 0.49133, 0.31373, 0.47059, 0.35649, 0.47059, 0.38613, 0.47106, 0.41619], "vertices": [-40.98, -54.66, -32.47, -71.23, -7.38, -76.18, 86.03, -68.77, 97.24, -57.12, 97.24, -43.46, 77.13, -32.29, 67.08, -11.46, 55.31, 1.19, 42.43, 10, 24.2, 11.87, 32.09, 21.09, 33.4, 34.75, 32.83, 47.22, -9.54, 65.82, -20.15, 64.29, -26.5, 58.19, -16.5, 50.05, -16.08, 48.63, -16.54, 47.47, -17.32, 47.21, -33.7, 55.57, -38.87, 54.32, -43.01, 50.94, -49.73, 45.44, -60.44, 27.25, -65.1, 4.55, -63.26, -7.57, -55.37, -12.65, -40.7, -9.27, -30.83, -15.47, -22.8, -35.63, -23.5, -45.79, -38.17, -50.86, 15.63, 8.02, 5.61, 5.18, -3.41, 3.01, -13.26, 2.51, -22.28, 3.01, -34.97, 3.51, -45.32, 2.5, -47.92, 1.81, -53.3, -2.2, -58.49, -4.09, -51.9, -4, -45.99, -3.49, -39.85, -2.76, -34.67, -3.05, -41.1, 0.97, -35.07, 49.42, 17.78, 24.51, 18.26, 22.51, 16.67, 16.85, 15.85, 12.09, 14.33, 21.27, 10.95, 15.2, 10.95, 10.99, 11.02, 6.72], "width": 163, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 20, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 54, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 80, 44, 46, 46, 48, 46, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 68, 100, 108, 108, 110, 110, 112, 68, 114, 114, 70, 112, 114, 106, 110], "type": "mesh", "hull": 34, "height": 142}}, "bonus5": {"bonus5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [23.15, 26.47, -8.85, 26.47, -8.85, 55.47, 23.15, 55.47], "width": 32, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 29}}, "satang3": {"satang3": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 9, -15.44, -15.94, 1, 1, 9, -8.16, 13.17, 1, 1, 9, -0.4, 11.23, 1, 1, 9, -7.68, -17.88, 1], "width": 30, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 8}}, "bonus6": {"bonus6": {"x": 0.5, "width": 141, "y": 0.5, "height": 141}}, "satang2": {"satang2": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 8, 39.54, -13.06, 1, 1, 8, 52.64, 39.33, 1, 1, 8, 73.01, 34.23, 1, 1, 8, 59.91, -18.15, 1], "width": 54, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 21}}, "bonus3": {"bonus3": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [48.22, -41.85, -25.78, -41.85, -25.78, 7.15, 48.22, 7.15], "width": 74, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "satang1": {"satang1": {"x": 0.88, "width": 142, "y": 0.65, "height": 142}}, "bonus4": {"bonus4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [7.35, 0.18, -49.65, 0.18, -49.65, 43.18, 7.35, 43.18], "width": 57, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 43}}}}, "skeleton": {"images": "", "width": 206, "spine": "3.7.93", "audio": "", "hash": "ktnW/+Hzoyf9BSU0zHruRzqXABo", "height": 152.97}, "slots": [{"attachment": "batgioiitem2", "name": "batgioiitem2", "bone": "root"}, {"attachment": "batgioiitem3", "name": "batgioiitem3", "bone": "root"}, {"attachment": "batgioiitem4", "name": "batgioiitem4", "bone": "root"}, {"attachment": "batgioiitem7", "name": "batgioiitem7", "bone": "root"}, {"attachment": "batgioiitem6", "name": "batgioiitem6", "bone": "root"}, {"attachment": "satang1", "name": "satang1", "bone": "root"}, {"attachment": "satang2", "name": "satang2", "bone": "root"}, {"attachment": "satang3", "name": "satang3", "bone": "root"}, {"attachment": "batgioiitem5", "name": "batgioiitem5", "bone": "root"}, {"attachment": "satang4", "name": "satang4", "bone": "root"}, {"attachment": "free3", "name": "free3", "bone": "root"}, {"attachment": "free4", "name": "free4", "bone": "root"}, {"attachment": "free5", "name": "free5", "bone": "root"}, {"attachment": "free6", "name": "free6", "bone": "root"}, {"attachment": "free1", "name": "free1", "bone": "root"}, {"attachment": "free7", "name": "free7", "bone": "root"}, {"attachment": "free8", "blend": "additive", "name": "free8", "bone": "root"}, {"attachment": "free10", "blend": "additive", "name": "free10", "bone": "bone20"}, {"attachment": "free10", "blend": "additive", "name": "free14", "bone": "bone24"}, {"attachment": "free10", "blend": "additive", "name": "free12", "bone": "bone21"}, {"attachment": "free10", "blend": "additive", "name": "free13", "bone": "bone22"}, {"attachment": "free9", "name": "free9", "bone": "root"}, {"attachment": "free11", "name": "free11", "bone": "bone23"}, {"name": "fire30", "bone": "bone26"}, {"attachment": "free11", "blend": "additive", "name": "free15", "bone": "bone25"}, {"attachment": "jackpot2", "name": "jackpot2", "bone": "root"}, {"attachment": "jackpot1", "name": "jackpot1", "bone": "root"}, {"attachment": "jackpot3", "name": "jackpot3", "bone": "root"}, {"attachment": "jackpot3", "blend": "additive", "name": "jackpot14", "bone": "root"}, {"attachment": "jackpot4", "name": "jackpot4", "bone": "bone27"}, {"attachment": "jackpot4", "name": "jackpot6", "bone": "bone28"}, {"attachment": "jackpot4", "name": "jackpot10", "bone": "bone32"}, {"attachment": "jackpot4", "name": "jackpot7", "bone": "bone29"}, {"attachment": "jackpot4", "name": "jackpot8", "bone": "bone30"}, {"attachment": "jackpot4", "name": "jackpot9", "bone": "bone31"}, {"attachment": "jackpot5", "name": "jackpot5", "bone": "bone33"}, {"attachment": "jackpot5", "name": "jackpot11", "bone": "bone34"}, {"attachment": "jackpot5", "name": "jackpot12", "bone": "bone35"}, {"attachment": "jackpot5", "name": "jackpot13", "bone": "bone36"}, {"attachment": "wild4", "name": "wild4", "bone": "root"}, {"attachment": "wild5", "name": "wild5", "bone": "root"}, {"attachment": "wild6", "name": "wild6", "bone": "root"}, {"attachment": "wild7", "name": "wild7", "bone": "root"}, {"attachment": "wild8", "name": "wild8", "bone": "root"}, {"attachment": "wild9", "name": "wild9", "bone": "root"}, {"attachment": "wild0", "name": "wild0", "bone": "root"}, {"attachment": "wild1", "name": "wild1", "bone": "root"}, {"attachment": "wild2", "name": "wild2", "bone": "root"}, {"attachment": "wild10", "name": "wild10", "bone": "root"}, {"attachment": "wild3", "name": "wild3", "bone": "root"}, {"attachment": "bonus6", "name": "bonus6", "bone": "root"}, {"attachment": "batgioiitem1", "name": "batgioiitem1", "bone": "root"}, {"name": "fire100", "bone": "bone42"}, {"attachment": "bonus2", "name": "bonus2", "bone": "root"}, {"attachment": "bonus3", "blend": "additive", "name": "bonus3", "bone": "root"}, {"attachment": "bonus1", "name": "bonus1", "bone": "root"}, {"attachment": "bonus1", "blend": "additive", "name": "bonus7", "bone": "root"}, {"attachment": "bonus4", "blend": "additive", "name": "bonus4", "bone": "root"}, {"attachment": "bonus5", "blend": "additive", "name": "bonus5", "bone": "root"}, {"attachment": "wild11", "name": "wild11", "bone": "root"}, {"attachment": "wild13", "name": "wild13", "bone": "bone57"}, {"attachment": "wild13", "blend": "additive", "name": "wild14", "bone": "bone58"}, {"attachment": "Monkeyking10", "name": "Monkeyking10", "bone": "root"}, {"attachment": "Monkeyking11", "name": "Monkeyking11", "bone": "root"}], "bones": [{"name": "root"}, {"parent": "root", "rotation": 88.09, "name": "bone", "length": 13.81, "x": -0.32, "y": -70.21}, {"parent": "bone", "rotation": -10.74, "name": "bone2", "length": 23.1, "x": 50.99, "y": 9.75}, {"parent": "bone2", "rotation": -68.07, "name": "bone3", "length": 11.42, "x": 60.83, "y": -27.36}, {"parent": "bone3", "rotation": -14.76, "name": "bone4", "length": 12.02, "x": 11.38, "y": -0.23}, {"parent": "bone2", "rotation": 38.52, "name": "bone5", "length": 9.61, "x": 55.98, "y": 21.22}, {"parent": "bone5", "rotation": 117.06, "name": "bone6", "length": 7.39, "x": 13.01, "y": 0.1}, {"parent": "root", "rotation": 80.91, "name": "bone7", "length": 25.67, "x": 8.45, "y": -70.51}, {"parent": "bone7", "rotation": 23.13, "name": "bone8", "length": 21.6, "x": 43.31, "y": -1.11}, {"parent": "bone8", "name": "bone9", "x": 63.66, "y": 14.92}, {"parent": "bone8", "name": "bone10", "x": 33.86, "y": 19.76}, {"parent": "root", "rotation": 105.05, "name": "bone11", "length": 23.54, "x": -3.22, "y": -69.1}, {"parent": "bone11", "rotation": -32.22, "name": "bone12", "length": 49.18, "x": 32.92, "y": -1.14}, {"parent": "bone12", "rotation": 113.75, "name": "bone13", "length": 5, "x": 80.78, "y": 3.17}, {"parent": "bone13", "rotation": 33.48, "name": "bone14", "length": 10.98, "x": 5}, {"parent": "bone14", "rotation": 37.63, "name": "bone15", "length": 21.5, "x": 11.1, "y": 0.15}, {"parent": "bone15", "rotation": -4.95, "name": "bone16", "length": 20.6, "x": 21.69, "y": 0.04}, {"parent": "bone16", "rotation": -20.05, "name": "bone17", "length": 25.21, "x": 20.66, "y": -0.18}, {"parent": "bone12", "rotation": 145.67, "name": "bone18", "length": 10.74, "x": 39.22, "y": 41.3}, {"parent": "bone18", "rotation": -2, "name": "bone19", "length": 11.88, "x": 15.63, "y": 0.01}, {"scaleX": 0.01, "parent": "root", "rotation": 90, "name": "bone20", "length": 16.24, "x": -23.23, "y": -61.46}, {"scaleX": 0.01, "parent": "root", "rotation": 90, "name": "bone21", "length": 16.24, "x": 12.41, "y": -57.68}, {"scaleX": 0.01, "parent": "root", "rotation": 90, "name": "bone22", "length": 16.24, "x": 36.84, "y": -62.46}, {"parent": "root", "rotation": 90, "name": "bone23", "length": 13.47, "x": 1.23, "y": -47.59}, {"scaleX": 0.01, "parent": "root", "rotation": 90, "name": "bone24", "length": 16.24, "x": -44.72, "y": -44.93}, {"parent": "root", "rotation": 90, "name": "bone25", "length": 13.47, "x": 1.23, "y": -47.59}, {"scaleX": 1.243, "parent": "root", "scaleY": 0.897, "name": "bone26", "x": -3.55, "y": -44.46}, {"parent": "root", "rotation": 90, "name": "bone27", "length": 8.57, "x": 39.55, "y": -12.96}, {"parent": "root", "rotation": 90, "name": "bone28", "length": 8.57, "x": -10.5, "y": -8.72}, {"parent": "root", "rotation": 90, "name": "bone29", "length": 8.57, "x": 10.68, "y": 6.29}, {"parent": "root", "rotation": 90, "name": "bone30", "length": 8.57, "x": -30.36, "y": 6.29}, {"parent": "root", "rotation": 90, "name": "bone31", "length": 8.57, "x": -47.64, "y": -6.67}, {"parent": "root", "rotation": 90, "name": "bone32", "length": 8.57, "x": 2.54, "y": -2.8}, {"parent": "root", "name": "bone33", "x": 34.9, "y": -9.98}, {"parent": "root", "name": "bone34", "x": -40.44, "y": -9.98}, {"parent": "root", "name": "bone35", "x": -22.96, "y": 18.53}, {"parent": "root", "name": "bone36", "x": 8.79, "y": -0.71}, {"parent": "root", "name": "bone37", "x": 0.24, "y": -59.36}, {"parent": "bone37", "name": "bone38", "x": -67.73, "y": 24.07}, {"parent": "bone37", "name": "bone39", "x": -22.91, "y": 24.07}, {"parent": "bone37", "name": "bone40", "x": 27.22, "y": 24.07}, {"parent": "bone37", "name": "bone41", "x": 67.73, "y": 24.07}, {"parent": "root", "rotation": -179.05, "name": "bone42", "length": 50.59}, {"parent": "root", "name": "bone43", "x": 1.47, "y": -77.76}, {"parent": "bone43", "name": "bone44", "x": -63.11, "y": 30.19}, {"parent": "bone43", "name": "bone45", "x": -32.92, "y": 30.19}, {"parent": "bone43", "name": "bone46", "x": 1.2, "y": 30.19}, {"parent": "bone43", "name": "bone47", "x": 35.97, "y": 30.19}, {"parent": "bone43", "name": "bone48", "x": 67.91, "y": 30.19}, {"parent": "root", "rotation": 94.22, "name": "bone49", "length": 36.55, "x": 6.15, "y": -64.13}, {"parent": "bone49", "rotation": -13.82, "name": "bone50", "length": 54.35, "x": 51.51, "y": -2.79}, {"parent": "bone50", "name": "bone51", "x": 37.96, "y": 2.01}, {"parent": "bone50", "rotation": 175.57, "name": "bone52", "length": 3.03, "x": 53.88, "y": 0.45}, {"parent": "bone52", "rotation": 28.84, "name": "bone53", "length": 9.45, "x": 6.67, "y": -7.58}, {"parent": "bone52", "rotation": 36.59, "name": "bone54", "length": 7.39, "x": 6.37, "y": 6.31}, {"parent": "bone50", "rotation": 58.64, "name": "bone55", "length": 4.48, "x": 31.43, "y": 42.7}, {"parent": "bone50", "rotation": -78.73, "name": "bone56", "length": 6.14, "x": 28.72, "y": -37.83}, {"parent": "root", "rotation": 90, "name": "bone57", "length": 11.8, "x": 0.92, "y": -51.99}, {"parent": "root", "rotation": 90, "name": "bone58", "length": 11.8, "x": 0.92, "y": -51.99}, {"parent": "root", "rotation": 79.6, "name": "bone59", "length": 67.82, "x": 13.31, "y": -67.6}, {"parent": "bone59", "rotation": 36.01, "name": "bone60", "length": 37.16, "x": 71.77, "y": 6.48}, {"parent": "bone60", "rotation": 165.07, "name": "bone61", "length": 6.58, "x": 41.05, "y": -8.89}, {"parent": "bone61", "rotation": -9.09, "name": "bone62", "length": 21.97, "x": 7.3, "y": -0.14}, {"parent": "bone62", "rotation": -18.1, "name": "bone63", "length": 32.19, "x": 22.46, "y": -0.01}, {"parent": "bone63", "rotation": -20.15, "name": "bone64", "length": 47.6, "x": 32.19}, {"parent": "bone59", "rotation": 121.77, "name": "bone65", "length": 4.54, "x": 83.9, "y": -14.03}, {"parent": "bone65", "rotation": 38.56, "name": "bone66", "length": 12.12, "x": 4.54}, {"parent": "bone66", "rotation": 9.66, "name": "bone67", "length": 25.33, "x": 12.12}, {"parent": "bone67", "rotation": -11.51, "name": "bone68", "length": 26.45, "x": 25.33}, {"parent": "bone68", "rotation": 6.6, "name": "bone69", "length": 22.8, "x": 26.86, "y": 0.04}], "animations": {"wildduongtank_1frame": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"color": [{"color": "ffffff00", "time": 0}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone49": {"translate": [{"x": 1.54, "y": 0.11, "time": 0}]}}}, "bonus": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "wild4": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "wild5": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "fire100": {"attachment": [{"name": "fire100", "time": 0.4333}, {"name": "fire101", "time": 0.5}, {"name": "fire102", "time": 0.6}, {"name": "fire103", "time": 0.6667}, {"name": "fire104", "time": 0.7333}, {"name": "fire105", "time": 0.8}, {"name": "fire106", "time": 0.9}, {"name": "fire107", "time": 0.9667}, {"name": "fire108", "time": 1.0333}, {"name": "fire109", "time": 1.1}, {"name": "fire110", "time": 1.2}, {"name": "fire111", "time": 1.2667}, {"name": null, "time": 1.3333}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild13": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"attachment": [{"name": null, "time": 0}]}, "bonus7": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff93", "time": 0.8333}, {"color": "ffffff00", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "bonus5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.5}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.5}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.5}]}}, "bones": {"bone48": {"scale": [{"x": 1, "y": 1, "time": 0.8333}, {"x": 1.225, "y": 1.225, "time": 1.1}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"x": 0, "y": 0, "time": 0.8333}, {"x": 6.5, "y": 0, "time": 1.1}, {"x": 0, "y": 0, "time": 1.3333}]}, "bone42": {"rotate": [{"angle": 0, "time": 0}, {"angle": -77.83, "time": 1.3333}]}, "bone46": {"scale": [{"x": 1, "y": 1, "time": 0.6}, {"x": 1.225, "y": 1.225, "time": 0.8333}, {"x": 1, "y": 1, "time": 1.1}]}, "bone47": {"scale": [{"x": 1, "y": 1, "time": 0.7}, {"x": 1.225, "y": 1.225, "time": 0.9667}, {"x": 1, "y": 1, "time": 1.2}], "translate": [{"x": 0, "y": 0, "time": 0.7}, {"x": 4.41, "y": 0, "time": 0.9667}, {"x": 0, "y": 0, "time": 1.2}]}, "bone44": {"scale": [{"x": 1, "y": 1, "time": 0.3333}, {"x": 1.225, "y": 1.225, "time": 0.6}, {"x": 1, "y": 1, "time": 0.8333}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": -6.96, "y": 0, "time": 0.6}, {"x": 0, "y": 0, "time": 0.8333}]}, "bone45": {"scale": [{"x": 1, "y": 1, "time": 0.4667}, {"x": 1.225, "y": 1.225, "time": 0.7}, {"x": 1, "y": 1, "time": 0.9667}], "translate": [{"x": 0, "y": 0, "time": 0.4667}, {"x": -3.71, "y": 0, "time": 0.7}, {"x": 0, "y": 0, "time": 0.9667}]}}, "deform": {"default": {"bonus2": {"bonus2": [{"vertices": [-2.37599, 0.19801, -2.37599, 0.19801, -5.93999, 0.198, -8.31602, -0.198, -8.118, 0.19801, -11.87999, -0.98999, -2.574, -2.37601, -2.376, -5.148, -2.37599, -3.564, -1.386, -0.39599, 0.324, 1.83599, 2.18398, 0.312, 4.36797, 0.46801, 4.36797, 0.312, 4.21198, 0.312, 4.67998, 1.092, 4.21198, -0.46799, 0.78001, 1.872, 1.40399, 1.09199, 1.24798, 1.09199, 1.55999, -0.46799, 0, 0, 2.02799, -0.78001, 0.77999, -0.312, 2.02798, -0.93599, 3.74399, -0.15599, 1.092, 0.15599, 0, 0, 0, 1.392, 0, 0, 0, 0, -3.36598, -0.19799, -3.168, 0.396, -2.37601, 0.396, 0.216, 4.26599, -0.106, 5.48397, 0, 6.06696, 0, 5.80197, 0, 5.05997, -1.20999, 4.57597, 0, 5.05997, -1.64001, 5.27699, -1.113, 6.89998, 0.603, 5.29798, 0.19801, 4.34799, 0.08701, 3.563, -0.29801, 3.20199, -1.09501, 2.37398, 0.372, 2.23199, 3.74397, 1.56, 1.87199, 2.96399, 0, 0, 0.432, 1.782, 0.54, 3.23999, 0.432, 0.594, 1.43999, 2.79598, 0.408, 3.11998, 0, 5.05997], "time": 0}, {"offset": 59, "vertices": [1.239, 1.23898, -1.416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.74499, 1.48999], "time": 0.3333}, {"vertices": [-2.37599, 0.19801, -2.37599, 0.19801, -5.93999, 0.198, -8.31602, -0.198, -8.118, 0.19801, -11.87999, -0.98999, -2.574, -2.37601, -2.376, -5.148, -2.37599, -3.564, -1.386, -0.39599, 0.324, 1.83599, 2.18398, 0.312, 4.36797, 0.46801, 4.36797, 0.312, 4.21198, 0.312, 4.67998, 1.092, 4.21198, -0.46799, 0.78001, 1.872, 1.40399, 1.09199, 1.24798, 1.09199, 1.55999, -0.46799, 0, 0, 2.02799, -0.78001, 0.77999, -0.312, 2.02798, -0.93599, 3.74399, -0.15599, 1.092, 0.15599, 0, 0, 0, 1.392, 0, 0, 0, 0, -3.36598, -0.19799, -3.168, 0.396, -2.37601, 0.396, 0.216, 4.26599, -0.106, 5.48397, 0, 6.06696, 0, 5.80197, 0, 5.05997, -1.20999, 4.57597, 0, 5.05997, -1.64001, 5.27699, -1.113, 6.89998, 0.603, 5.29798, 0.19801, 4.34799, 0.08701, 3.563, -0.29801, 3.20199, -1.09501, 2.37398, 0.372, 2.23199, 3.74397, 1.56, 1.87199, 2.96399, 0, 0, 0.432, 1.782, 0.54, 3.23999, 0.432, 0.594, 1.43999, 2.79598, 0.408, 3.11998, 0, 5.05997], "time": 0.8333}, {"offset": 59, "vertices": [1.239, 1.23898, -1.416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.74499, 1.48999], "time": 1.3333}, {"vertices": [-2.37599, 0.19801, -2.37599, 0.19801, -5.93999, 0.198, -8.31602, -0.198, -8.118, 0.19801, -11.87999, -0.98999, -2.574, -2.37601, -2.376, -5.148, -2.37599, -3.564, -1.386, -0.39599, 0.324, 1.83599, 2.18398, 0.312, 4.36797, 0.46801, 4.36797, 0.312, 4.21198, 0.312, 4.67998, 1.092, 4.21198, -0.46799, 0.78001, 1.872, 1.40399, 1.09199, 1.24798, 1.09199, 1.55999, -0.46799, 0, 0, 2.02799, -0.78001, 0.77999, -0.312, 2.02798, -0.93599, 3.74399, -0.15599, 1.092, 0.15599, 0, 0, 0, 1.392, 0, 0, 0, 0, -3.36598, -0.19799, -3.168, 0.396, -2.37601, 0.396, 0.216, 4.26599, -0.106, 5.48397, 0, 6.06696, 0, 5.80197, 0, 5.05997, -1.20999, 4.57597, 0, 5.05997, -1.64001, 5.27699, -1.113, 6.89998, 0.603, 5.29798, 0.19801, 4.34799, 0.08701, 3.563, -0.29801, 3.20199, -1.09501, 2.37398, 0.372, 2.23199, 3.74397, 1.56, 1.87199, 2.96399, 0, 0, 0.432, 1.782, 0.54, 3.23999, 0.432, 0.594, 1.43999, 2.79598, 0.408, 3.11998, 0, 5.05997], "time": 1.6667}]}, "bonus5": {"bonus5": [{"time": 0.8333}, {"vertices": [3.582, 4.776, 3.582, 4.776, 3.582, 4.776, 3.582, 4.776], "time": 1.3333}]}, "bonus3": {"bonus3": [{"time": 0.8333}, {"vertices": [7.84227, 6.00013, -7.84227, 6.00013, -7.84227, 16.38585, 7.84227, 16.38585], "time": 1.3333}]}, "bonus4": {"bonus4": [{"time": 0.8333}, {"vertices": [2.69297, 1.90425, -6.67295, 1.90425, -6.67295, 8.96976, 2.69297, 8.96976], "time": 1.3333}]}}}}, "wildduongtank": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "time": 1.3333}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone59": {"rotate": [{"angle": 0, "time": 0}, {"angle": -0.59, "time": 0.8333}, {"angle": 0, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -1.74, "y": 0.5, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone49": {"translate": [{"x": 1.54, "y": 0.11, "time": 0}]}, "bone60": {"rotate": [{"angle": 0, "time": 0}, {"angle": -6.06, "time": 0.8333}, {"angle": 0, "time": 1.6667}]}, "bone64": {"rotate": [{"angle": 0, "time": 0}, {"angle": -7.26, "time": 0.3333}, {"angle": 6.04, "time": 0.6333}, {"angle": -2.94, "time": 0.9333}, {"angle": 20.2, "time": 1.2}, {"angle": -13.08, "time": 1.4667}, {"angle": 0, "time": 1.6667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.985, "y": 1, "time": 1.2}]}, "bone62": {"rotate": [{"angle": 0, "time": 0}, {"angle": -2.09, "time": 0.3333}, {"angle": -1.66, "time": 0.6333}, {"angle": -0.79, "time": 0.9333}, {"angle": -9.24, "time": 1.2}, {"angle": -2.66, "time": 1.4667}, {"angle": 0, "time": 1.6667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.985, "y": 1, "time": 1.2}]}, "bone63": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.03, "time": 0.3333}, {"angle": -2.36, "time": 0.6333}, {"angle": -0.06, "time": 0.9333}, {"angle": -9.01, "time": 1.2}, {"angle": -1.78, "time": 1.4667}, {"angle": 0, "time": 1.6667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.985, "y": 1, "time": 1.2}, {"x": 1, "y": 1, "time": 1.6667}]}, "bone57": {"scale": [{"x": 1, "y": 1, "time": 0.1667}, {"x": 1.363, "y": 1.363, "time": 0.5}, {"x": 1, "y": 1, "time": 0.6}]}, "bone68": {"rotate": [{"angle": 0, "time": 0}, {"angle": -1.66, "time": 0.5}, {"angle": 8.6, "time": 0.9667}, {"angle": 0, "time": 1.6667}]}, "bone58": {"scale": [{"x": 1, "y": 1, "time": 0.1667}, {"x": 1.363, "y": 1.363, "time": 0.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.6}, {"x": 1, "y": 1, "time": 0.8333}, {"x": 1.121, "y": 1.098, "time": 1.2667}, {"x": 1.102, "y": 1.077, "time": 1.3333}]}, "bone69": {"rotate": [{"angle": 0, "time": 0}, {"angle": -12.05, "time": 0.5}, {"angle": -17.28, "time": 0.9667}, {"angle": 0, "time": 1.6667}]}, "bone66": {"rotate": [{"angle": 0, "time": 0}, {"angle": 0.01, "time": 0.5}, {"angle": 2.7, "time": 0.9667}, {"angle": 0, "time": 1.6667}]}, "bone67": {"rotate": [{"angle": 0, "time": 0}, {"angle": -1.8, "time": 0.5}, {"angle": -0.82, "time": 0.9667}, {"angle": 0, "time": 1.6667}]}}, "deform": {"default": {"wild1": {"wild1": [{"time": 0.6}, {"offset": 102, "curve": "stepped", "vertices": [-0.60921, -0.50191, -0.78864, -0.03422, -2.31321, -1.98388, -3.04145, -0.19234, -3.8054, -3.23272, -4.98476, -0.29172, -3.91483, -2.99713, -4.93036, -0.03773, -3.41006, -1.61716, -3.69675, 0.76061, -1.85844, -0.67767, -1.89215, 0.57719, -0.14749, 0.09998, -0.05764, 0.16861, 0, 0, 0, 0, 0, 0, 0, 0, -0.03319, 0.50684, 0.27851, 0.42475, 0.50476, 0.31377, 0.30935, 0.05941, 0.28281, -0.13872, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80669, -0.47569, -0.93058, 0.10558, -2.67877, -1.86675, -3.26299, 0.12123, -3.46458, -2.23963, -4.11494, 0.29631, -3.18204, -2.37479, -3.97061, 0.01834, -1.49459, -1.57006, -2.1386, -0.35448, -0.52724, -0.22346, -0.55563, 0.13885, 0.34459, 0.43276, 0.53563, 0.13824, 0.27356, 0.37839, 0.44619, 0.13756, 0.10741, -0.00817], "time": 0.6667}, {"offset": 102, "vertices": [-0.60921, -0.50191, -0.78864, -0.03422, -2.31321, -1.98388, -3.04145, -0.19234, -3.8054, -3.23272, -4.98476, -0.29172, -3.91483, -2.99713, -4.93036, -0.03773, -3.41006, -1.61716, -3.69675, 0.76061, -1.85844, -0.67767, -1.89215, 0.57719, -0.14749, 0.09998, -0.05764, 0.16861, 0, 0, 0, 0, 0, 0, 0, 0, -0.03319, 0.50684, 0.27851, 0.42475, 0.50476, 0.31377, 0.30935, 0.05941, 0.28281, -0.13872, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80669, -0.47569, -0.93058, 0.10558, -2.67877, -1.86675, -3.26299, 0.12123, -3.46458, -2.23963, -4.11494, 0.29631, -3.18204, -2.37479, -3.97061, 0.01834, -1.49459, -1.57006, -2.1386, -0.35448, -0.52724, -0.22346, -0.55563, 0.13885, 0.34459, 0.43276, 0.53563, 0.13824, 0.27356, 0.37839, 0.44619, 0.13756, 0.10741, -0.00817], "time": 0.7333}, {"time": 0.8}]}}}}, "batgioi": {"slots": {"satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "wild4": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "wild5": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild13": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"attachment": [{"name": null, "time": 0}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone3": {"rotate": [{"angle": 0, "time": 0}, {"angle": 3.03, "time": 0.3333}, {"angle": 2.12, "time": 0.6667}, {"angle": 1.12, "time": 1}, {"angle": 1.51, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone2": {"rotate": [{"angle": 0, "time": 0}, {"curve": "stepped", "angle": 6.09, "time": 0.3333}, {"angle": 6.09, "time": 1.3333}, {"angle": 0, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 1.98, "y": 0.15, "time": 0.5}, {"x": 0, "y": 0, "time": 0.6667}, {"x": 1.98, "y": 0.15, "time": 0.8333}, {"x": 0, "y": 0, "time": 1}, {"x": 1.98, "y": 0.15, "time": 1.1667}, {"x": 0, "y": 0, "time": 1.3333}]}, "bone4": {"rotate": [{"angle": 0, "time": 0}, {"angle": -26.66, "time": 0.3333}, {"angle": -4.52, "time": 0.6667}, {"angle": -11.1, "time": 1}, {"angle": 1.75, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone6": {"rotate": [{"angle": 0, "time": 0}, {"angle": 10.28, "time": 0.3333}, {"angle": 5.58, "time": 0.6667}, {"angle": -7.58, "time": 1}, {"angle": 8.41, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}}, "deform": {"default": {"batgioiitem5": {"batgioiitem5": [{"time": 0.3333}, {"offset": 68, "vertices": [-0.91385, -0.59632, -1.53379, -0.54482, -1.74831, -0.8151, -1.54773, -0.42361, -1.81801, -0.2091, -1.42653, -0.40968, 0, 0, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516, 1.6968, 0.19516], "time": 0.5}, {"time": 0.6667}, {"offset": 68, "vertices": [-0.91385, -0.59632, -1.53379, -0.54482, -1.74831, -0.8151, -1.54773, -0.42361, -1.81801, -0.2091, -1.42653, -0.40968, 0, 0, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334, 1.33321, 0.15334], "time": 0.8333}, {"time": 1}, {"offset": 68, "vertices": [-0.91385, -0.59632, -1.53379, -0.54482, -1.74831, -0.8151, -1.54773, -0.42361, -1.81801, -0.2091, -1.42653, -0.40968, 0, 0, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516, 1.69681, 0.19516], "time": 1.1667}, {"time": 1.3333}, {"offset": 12, "curve": "stepped", "vertices": [-2.44472, -0.94858, -4.30469, -1.44965, -4.97541, -1.21352, -4.61362, -1.54199, -2.77311, -1.31039, 0, 0, 0.34858, -0.22326, 0.67698, 0.13855, 0.96603, 0.38855, 1.05203, -0.29501, 1.26239, -0.26856], "time": 1.3667}, {"offset": 12, "vertices": [-2.44472, -0.94858, -4.30469, -1.44965, -4.97541, -1.21352, -4.61362, -1.54199, -2.77311, -1.31039, 0, 0, 0.34858, -0.22326, 0.67698, 0.13855, 0.96603, 0.38855, 1.05203, -0.29501, 1.26239, -0.26856], "time": 1.4667}, {"time": 1.5333}]}, "batgioiitem3": {"batgioiitem3": [{"time": 0.3333}, {"vertices": [1.44817, 0.04827, 0, 0, 0, 0, 0, 0, 1.44817, 0.04827, 0, 0, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827], "time": 0.5}, {"time": 0.6667}, {"vertices": [1.44817, 0.04827, 0, 0, 0, 0, 0, 0, 1.44817, 0.04827, 0, 0, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827], "time": 0.8333}, {"time": 1}, {"vertices": [1.44817, 0.04827, 0, 0, 0, 0, 0, 0, 1.44817, 0.04827, 0, 0, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827, 1.44817, 0.04827], "time": 1.1667}, {"time": 1.3333}]}}}}, "Free": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free12": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "time": 0.8333}, {"color": "ffffff00", "time": 1.5}]}, "free13": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 1.1}, {"color": "ffffff00", "time": 1.5}]}, "free14": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "time": 1.1333}, {"color": "ffffff00", "time": 1.6667}]}, "free10": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.3333}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffff7d", "curve": "stepped", "time": 0.8333}, {"color": "ffffff7d", "time": 1.1667}, {"color": "ffffff00", "time": 1.5}]}, "wild4": {"attachment": [{"name": null, "time": 0}]}, "wild5": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffa1", "time": 0.6667}, {"color": "ffffff52", "time": 1}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"color": [{"color": "ffffff00", "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.6667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild13": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"attachment": [{"name": null, "time": 0}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone17": {"rotate": [{"angle": 0, "time": 0}, {"angle": 1.57, "time": 0.3333}, {"angle": -9.73, "time": 0.6667}, {"angle": 11.54, "time": 1}, {"angle": 4.83, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone15": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.61, "time": 0.3333}, {"angle": -0.08, "time": 0.6667}, {"angle": -3.36, "time": 1}, {"angle": -4.37, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone16": {"rotate": [{"angle": 0, "time": 0}, {"angle": -7.69, "time": 0.3333}, {"angle": 0.8, "time": 0.6667}, {"angle": -5.38, "time": 1}, {"angle": -7, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone20": {"scale": [{"curve": "stepped", "x": 1, "y": 0.1, "time": 0}, {"x": 1, "y": 0.1, "time": 0.5}, {"x": 70, "y": 0.7, "time": 0.9667}, {"x": 100, "y": 1, "time": 1.3333}], "translate": [{"x": 0, "y": 0, "time": 0.5}, {"x": 0, "y": 38.59, "time": 1.3333}]}, "bone21": {"scale": [{"curve": "stepped", "x": 1, "y": 0.1, "time": 0}, {"x": 1, "y": 0.1, "time": 0.3333}, {"x": 70, "y": 0.7, "time": 0.8}, {"x": 134.269, "y": 1.343, "time": 1.5}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 76.21, "time": 1.5}]}, "bone24": {"scale": [{"curve": "stepped", "x": 1, "y": 0.1, "time": 0}, {"x": 1, "y": 0.1, "time": 0.3333}, {"x": 70, "y": 0.7, "time": 1.0667}, {"x": 100, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 38.59, "time": 1.6667}]}, "bone14": {"rotate": [{"angle": 0, "time": 0}, {"angle": 0.92, "time": 0.3333}, {"angle": 0.08, "time": 0.6667}, {"angle": 1.67, "time": 1}, {"angle": 1.94, "time": 1.3333}, {"angle": 0, "time": 1.6667}]}, "bone25": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.798, "y": 1, "time": 0.5}, {"x": 1, "y": 1, "time": 0.6667}, {"x": 1.058, "y": 1, "time": 0.8}, {"x": 0.941, "y": 1, "time": 0.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9667}, {"x": 1, "y": 1, "time": 1.1667}, {"x": 1.207, "y": 1.112, "time": 1.5}, {"x": 1, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": -5.8, "time": 0.5}, {"x": 0, "y": 5.33, "time": 0.6667}, {"x": 0, "y": 5.19, "time": 0.8}, {"x": 0, "y": -1.7, "time": 0.9}, {"x": 0, "y": -0.23, "time": 0.9667}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone22": {"scale": [{"curve": "stepped", "x": 1, "y": 0.1, "time": 0}, {"x": 1, "y": 0.1, "time": 0.5}, {"x": 70, "y": 0.7, "time": 1.0667}, {"x": 131.733, "y": 1.317, "time": 1.5}], "translate": [{"x": 0, "y": 0, "time": 0.5}, {"x": 0, "y": 60.26, "time": 1.5}]}, "bone12": {"rotate": [{"angle": 3.18, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 1.5}, {"angle": 3.18, "time": 1.6667}]}, "bone23": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.798, "y": 1, "time": 0.5}, {"x": 1, "y": 1, "time": 0.6667}, {"x": 1.058, "y": 1, "time": 0.8}, {"x": 0.941, "y": 1, "time": 0.9}, {"x": 1, "y": 1, "time": 0.9667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": -5.8, "time": 0.5}, {"x": 0, "y": 5.33, "time": 0.6667}, {"x": 0, "y": 5.19, "time": 0.8}, {"x": 0, "y": -1.7, "time": 0.9}, {"x": 0, "y": -0.23, "time": 0.9667}, {"x": 0, "y": 0, "time": 1.6667}]}}, "deform": {"default": {"free6": {"free6": [{"time": 0}, {"offset": 38, "vertices": [0.79881, 0.521, -0.36582, -0.88074, 2.80141, 1.1397, -1.67063, -2.52107, -1.58165, -2.57784, -1.91648, -3.47081, -3.78244, -6.65309, -2.27449, -4.83556, -1.93624, -2.64455, -1.84277, -2.71051, -1.32346, -2.29505], "time": 0.4333}, {"time": 0.8333}, {"offset": 38, "vertices": [0.79881, 0.521, -0.36582, -0.88074, -1.41342, -2.08195, -0.00702, 2.51639, -0.09474, 2.51456, 0.89514, 3.41174, -0.56225, 1.88274, 2.00716, 1.61103, 1.58206, 4.93496, 1.409, 4.98709, -0.23303, 2.86357, -1.01962, -1.13766, 0.20037, 1.51451], "time": 1.2667}, {"time": 1.6667}]}, "free4": {"free4": [{"time": 0}, {"offset": 2, "vertices": [0.95383, 1.25237, 1.79965, 2.85441, 0.69656, 2.70514, 0.13079, 3.29648, 0.172, 2.54309, 4.16782, 2.62841], "time": 0.4333}, {"time": 0.8333}, {"offset": 2, "vertices": [0.95383, 1.25237, 1.79965, 2.85441, 0.69656, 2.70514, 0.13079, 3.29648, 0.172, 2.54309, 4.16782, 2.62841], "time": 1.2667}, {"time": 1.6667}]}, "free5": {"free5": [{"vertices": [-3.91, 3.45, 0, 0, -2.99, 1.61002, -1.83999, 0.69001, -2.07, 0.69001, -1.84, 0.68999, -2.29999, 0.22999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.45, 1.84], "time": 0}, {"curve": "stepped", "time": 0.3333}, {"time": 1.5}, {"vertices": [-3.91, 3.45, 0, 0, -2.99, 1.61002, -1.83999, 0.69001, -2.07, 0.69001, -1.84, 0.68999, -2.29999, 0.22999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.45, 1.84], "time": 1.6667}]}}}, "drawOrder": [{"offsets": [{"offset": -29, "slot": "batgioiitem1"}], "time": 0}]}, "wildmonkey_1frame": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"color": [{"color": "ffffff00", "time": 0}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone49": {"translate": [{"x": 1.54, "y": 0.11, "time": 0}]}}}, "Jackpot": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "curve": "stepped", "time": 0.8333}, {"color": "ffffffff", "time": 1.4333}, {"color": "ffffff00", "time": 1.6667}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.6}, {"color": "ffffffff", "time": 1.4}, {"color": "ffffff00", "time": 1.6667}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.2333}, {"color": "ffffffff", "time": 0.9333}, {"color": "ffffff00", "time": 1.1667}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "wild4": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "wild5": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "fire30": {"attachment": [{"name": "fire30", "time": 0}, {"name": "fire31", "time": 0.0667}, {"name": "fire32", "time": 0.1333}, {"name": "fire33", "time": 0.2333}, {"name": "fire30", "time": 0.3}, {"name": "fire31", "time": 0.3667}, {"name": "fire32", "time": 0.4333}, {"name": "fire33", "time": 0.5}, {"name": "fire30", "time": 0.5667}, {"name": "fire31", "time": 0.6667}, {"name": "fire32", "time": 0.7333}, {"name": "fire33", "time": 0.8}, {"name": "fire30", "time": 0.8667}, {"name": "fire31", "time": 0.9333}, {"name": "fire32", "time": 1}, {"name": "fire33", "time": 1.1}, {"name": "fire30", "time": 1.1667}, {"name": "fire31", "time": 1.2333}, {"name": "fire32", "time": 1.3}, {"name": "fire33", "time": 1.3667}, {"name": "fire30", "time": 1.4333}, {"name": "fire31", "time": 1.5333}, {"name": "fire32", "time": 1.6}, {"name": "fire33", "time": 1.6667}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff74", "time": 0.3333}, {"color": "ffffff00", "curve": "stepped", "time": 0.6667}, {"color": "ffffff00", "time": 1}, {"color": "ffffff74", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4333}, {"color": "ffffffff", "time": 1.2333}, {"color": "ffffff00", "time": 1.5}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem1": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.3333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "jackpot4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 0.7667}, {"color": "ffffff00", "time": 1}]}, "jackpot7": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.5}, {"color": "ffffffff", "time": 1.1}, {"color": "ffffff00", "time": 1.3333}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.3333}, {"color": "ffffffff", "time": 0.9333}, {"color": "ffffff00", "time": 1.1667}]}, "jackpot9": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.5}, {"color": "ffffffff", "time": 1.1}, {"color": "ffffff00", "time": 1.3333}]}, "jackpot8": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.2667}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8333}, {"color": "ffffff00", "time": 0.9333}, {"color": "ffffffff", "curve": "stepped", "time": 1.0333}, {"color": "ffffffff", "time": 1.4333}, {"color": "ffffff00", "time": 1.6}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild13": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"attachment": [{"name": null, "time": 0}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone28": {"scale": [{"curve": "stepped", "x": 0, "y": 1, "time": 0}, {"x": 0, "y": 1, "time": 0.1667}, {"x": 0.7, "y": 1, "time": 0.4333}, {"x": 1, "y": 1, "time": 1.1667}], "translate": [{"curve": "stepped", "x": 0, "y": 0.7, "time": 0}, {"x": 0, "y": 0.7, "time": 0.1667}, {"x": 0, "y": 45.64, "time": 1.1667}]}, "bone39": {"translate": [{"x": 0, "y": 0, "time": 0.1}, {"x": 0, "y": 6.92, "time": 0.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4667}, {"x": 0, "y": 0, "time": 0.9333}, {"x": 0, "y": 6.92, "time": 1.1333}, {"x": 0, "y": 0, "time": 1.3}]}, "bone29": {"scale": [{"curve": "stepped", "x": 0, "y": 1, "time": 0}, {"x": 0, "y": 1, "time": 0.3333}, {"x": 0.7, "y": 1, "time": 0.6}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.7, "time": 0}, {"x": 0, "y": 0.7, "time": 0.3333}, {"x": 0, "y": 45.64, "time": 1.3333}]}, "bone27": {"scale": [{"x": 0, "y": 1, "time": 0}, {"x": 0.7, "y": 1, "time": 0.2667}, {"x": 1, "y": 1, "time": 1}], "translate": [{"x": 0, "y": 0.7, "time": 0}, {"x": 0, "y": 45.64, "time": 1}]}, "bone38": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 6.92, "time": 0.2}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4}, {"x": 0, "y": 0, "time": 0.8333}, {"x": 0, "y": 6.92, "time": 1.0333}, {"x": 0, "y": 0, "time": 1.2333}]}, "bone31": {"scale": [{"curve": "stepped", "x": 0, "y": 1, "time": 0}, {"x": 0, "y": 1, "time": 0.3333}, {"x": 0.7, "y": 1, "time": 0.6}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.7, "time": 0}, {"x": 0, "y": 0.7, "time": 0.3333}, {"x": 0, "y": 45.64, "time": 1.3333}]}, "bone32": {"scale": [{"curve": "stepped", "x": 0, "y": 1, "time": 0}, {"x": 0, "y": 1, "time": 0.6667}, {"x": 0.7, "y": 1, "time": 0.9333}, {"x": 1, "y": 1, "time": 1.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0.7, "time": 0}, {"x": 0, "y": 0.7, "time": 0.6667}, {"x": 0, "y": 45.64, "time": 1.6667}]}, "bone40": {"translate": [{"x": 0, "y": 0, "time": 0.2}, {"x": 0, "y": 6.92, "time": 0.4}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.5667}, {"x": 0, "y": 0, "time": 1.0333}, {"x": 0, "y": 6.92, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.4}]}, "bone30": {"scale": [{"curve": "stepped", "x": 0, "y": 1, "time": 0}, {"x": 0, "y": 1, "time": 0.1667}, {"x": 0.7, "y": 1, "time": 0.3333}, {"x": 1, "y": 1, "time": 0.8333}, {"x": 0, "y": 1, "time": 0.9333}, {"x": 0.7, "y": 1, "time": 1.1}, {"x": 1, "y": 1, "time": 1.6}], "translate": [{"curve": "stepped", "x": 0, "y": 0.7, "time": 0}, {"x": 0, "y": 0.7, "time": 0.1667}, {"x": 0, "y": 45.64, "time": 0.8333}, {"x": 64.97, "y": 0.7, "time": 0.9333}, {"x": 68.07, "y": 28.84, "time": 1.6}]}, "bone41": {"translate": [{"x": 0, "y": 0, "time": 0.3}, {"x": 0, "y": 6.92, "time": 0.4667}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 1.1333}, {"x": 0, "y": 6.92, "time": 1.3}, {"x": 0, "y": 0, "time": 1.5}]}, "bone35": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 1, "y": 1, "time": 0.6}, {"x": 0.706, "y": 0.706, "time": 0.8667}, {"x": 1, "y": 1, "time": 1.1333}, {"x": 0.706, "y": 0.706, "time": 1.4}, {"x": 1, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 42.07, "time": 1.6667}]}, "bone36": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.1667}, {"x": 1, "y": 1, "time": 0.4333}, {"x": 0.706, "y": 0.706, "time": 0.7}, {"x": 1, "y": 1, "time": 0.9667}, {"x": 0.706, "y": 0.706, "time": 1.2333}, {"x": 1, "y": 1, "time": 1.5}], "translate": [{"x": 0, "y": 0, "time": 0.1667}, {"x": 0, "y": 42.07, "time": 1.5}]}, "bone33": {"scale": [{"x": 0, "y": 0, "time": 0}, {"x": 1, "y": 1, "time": 0.3333}, {"x": 0.706, "y": 0.706, "time": 0.6667}, {"x": 1, "y": 1, "time": 1}, {"x": 0.706, "y": 0.706, "time": 1.3333}, {"x": 1, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 42.07, "time": 1.6667}]}, "bone34": {"scale": [{"x": 0, "y": 0, "time": 0}, {"x": 1, "y": 1, "time": 0.2333}, {"x": 0.706, "y": 0.706, "time": 0.4667}, {"x": 1, "y": 1, "time": 0.7}, {"x": 0.706, "y": 0.706, "time": 0.9333}, {"x": 1, "y": 1, "time": 1.1667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 42.07, "time": 1.1667}]}}, "deform": {"default": {"jackpot1": {"jackpot1": [{"time": 0}, {"offset": 4, "vertices": [0.64259, 0.36719, 0.8262, 0.91799, 2.33999, 1.565, 2.34599, 2.34599, 3.136, 2.42199, 4.58397, 2.06999, 5.22237, 1.3668, 3.99022, -0.07345, 4.40639, 2.09304, 3.85559, -1.87271, 3.43367, 1.36951, 3.97431, 2.21014, 2.78287, 2.77111, 3.17271, 3.50158, 2.76255, 2.5711, 2.14223, 1.68126, 1.27112, 0.7, 1.106, 0.79, 1.26399, 0.158], "time": 0.2333}, {"offset": 4, "vertices": [-1.66099, -1.057, -1.963, -1.51001, -0.906, -2.11399, -0.75499, -1.963, -0.604, -2.26499, -2.11401, -2.71799, -3.473, -4.52999, -3.322, -3.02, -3.02001, -1.812, -1.96301, -1.963, -2.265, -1.963, -2.26501, -1.661, -2.416, -1.963, -1.75499, -2.69099, -0.585, -1.989, -0.585, -1.989, -0.70199, -1.98899, -0.46799, -1.75499, -0.351, -1.05301], "time": 0.5667}, {"time": 0.8333}, {"offset": 4, "vertices": [0.64259, 0.36719, 0.8262, 0.91799, 2.33999, 1.565, 2.34599, 2.34599, 3.136, 2.42199, 4.58397, 2.06999, 5.22237, 1.3668, 3.99022, -0.07345, 4.40639, 2.09304, 3.85559, -1.87271, 3.43367, 1.36951, 3.97431, 2.21014, 2.78287, 2.77111, 3.17271, 3.50158, 2.76255, 2.5711, 2.14223, 1.68126, 1.27112, 0.7, 1.106, 0.79, 1.26399, 0.158], "time": 1.0667}, {"offset": 4, "vertices": [-1.66099, -1.057, -1.963, -1.51001, -0.906, -2.11399, -0.75499, -1.963, -0.604, -2.26499, -2.11401, -2.71799, -3.473, -4.52999, -3.322, -3.02, -3.02001, -1.812, -1.96301, -1.963, -2.265, -1.963, -2.26501, -1.661, -2.416, -1.963, -1.75499, -2.69099, -0.585, -1.989, -0.585, -1.989, -0.70199, -1.98899, -0.46799, -1.75499, -0.351, -1.05301], "time": 1.4}, {"time": 1.6667}]}, "jackpot2": {"jackpot2": [{"time": 0}, {"offset": 2, "vertices": [0.72, 0.24, 0.23999, 2.63999, 1.67999, 7.67999, 3.12, 7.68, 2.87999, 7.92001, 0, 0, 0, 0, 0, 0, -4.79999, 8.87999, -2.64001, 5.04, -0.47998, 5.27999, 0.24, 1.67999], "time": 0.8333}, {"time": 1.6667}]}}}}, "satang": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "wild8": {"attachment": [{"name": null, "time": 0}]}, "wild9": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "wild6": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "wild7": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "wild4": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "wild5": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking11": {"attachment": [{"name": null, "time": 0}]}, "Monkeyking10": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "wild11": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild10": {"attachment": [{"name": null, "time": 0}]}, "wild13": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"attachment": [{"name": null, "time": 0}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone7": {"rotate": [{"angle": 0, "time": 0}, {"angle": 3.31, "time": 0.8333}, {"angle": 0, "time": 1.6667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.038, "y": 1.038, "time": 0.8333}, {"x": 1, "y": 1, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -0.02, "y": -0.15, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone8": {"rotate": [{"angle": 0, "time": 0}, {"angle": -1.04, "time": 0.8333}, {"angle": 0, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -0.52, "y": -4.05, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}}, "deform": {"default": {"satang4": {"satang4": [{"time": 0}, {"offset": 6, "vertices": [1.2859, -2.4678, 0.28245, -2.7684, 0.27796, -1.73789, -0.38421, -1.71753, 0.88273, -2.17584, 0.0158, -2.34806, 1.02877, -1.97424, 0.22596, -2.21474, 0.53516, -2.23145, -0.32773, -2.27121, 0.91054, -2.34962, -0.02262, -2.5198, 2.43991, -2.99612, 1.15929, -3.68596, 1.61273, -1.16786, 1.06667, -1.68141, -0.29844, -0.01869, -0.25593, 0.11206, -0.87634, 0.23105, -0.71718, 0.55409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.10241, 1.52934, 5.30012, -0.53169, 2.29388, 1.25824, 2.60045, 0.2885, 5.53442, 1.22149, 2.67254, 1.35662, 1.90638, 1.17634], "time": 0.1667}, {"time": 0.3333}, {"offset": 6, "vertices": [1.2859, -2.4678, 0.28245, -2.7684, 0.27796, -1.73789, -0.38421, -1.71753, 0.88273, -2.17584, 0.0158, -2.34806, 1.02877, -1.97424, 0.22596, -2.21474, 0.53516, -2.23145, -0.32773, -2.27121, 0.91054, -2.34962, -0.02262, -2.5198, 2.43991, -2.99612, 1.15929, -3.68596, 1.61273, -1.16786, 1.06667, -1.68141, -0.29844, -0.01869, -0.25593, 0.11206, -0.87634, 0.23105, -0.71718, 0.55409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.10241, 1.52934, 5.30012, -0.53169, 2.29388, 1.25824, 2.60045, 0.2885, 5.53442, 1.22149, 2.67254, 1.35662, 1.90638, 1.17634], "time": 0.5}, {"time": 0.6667}, {"offset": 6, "vertices": [1.2859, -2.4678, 0.28245, -2.7684, 0.27796, -1.73789, -0.38421, -1.71753, 0.88273, -2.17584, 0.0158, -2.34806, 1.02877, -1.97424, 0.22596, -2.21474, 0.53516, -2.23145, -0.32773, -2.27121, 0.91054, -2.34962, -0.02262, -2.5198, 2.43991, -2.99612, 1.15929, -3.68596, 1.61273, -1.16786, 1.06667, -1.68141, -0.29844, -0.01869, -0.25593, 0.11206, -0.87634, 0.23105, -0.71718, 0.55409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.10241, 1.52934, 5.30012, -0.53169, 2.29388, 1.25824, 2.60045, 0.2885, 5.53442, 1.22149, 2.67254, 1.35662, 1.90638, 1.17634], "time": 0.8333}, {"offset": 6, "vertices": [1.02872, -1.97424, 0.22596, -2.21472, 0.22237, -1.39031, -0.30737, -1.37402, 0.70618, -1.74067, 0.01264, -1.87845, 0.82302, -1.57939, 0.18077, -1.77179, 0.42812, -1.78516, -0.26218, -1.81697, 0.72843, -1.87969, -0.0181, -2.01584, 1.95193, -2.39689, 0.92743, -2.94877, 1.29019, -0.93429, 0.85333, -1.34513, -0.23875, -0.01496, -0.20474, 0.08965, -0.70107, 0.18484, -0.57374, 0.44327, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.08193, 1.22347, 4.24009, -0.42535, 1.8351, 1.00659, 2.08036, 0.2308, 4.42754, 0.97719, 2.13803, 1.0853, 1.5251, 0.94107, 0, 0, 0, 0, 0, 0, -3.03121, -0.93982, -3.16195, 0.27155, -4.24653, -1.69148, -4.57089, 0.03314, -3.367, -1.72549, -3.76897, -0.32976, -1.17976, -0.2891, -1.20177, 0.17671, 0, 0, 0, 0, 0.83814, 0.05873, 0.09109, -0.074, 0.05652, -0.10286, 0, 0, -2.29557, 0.27883, -3.75959, 0.44537, -3.82768, 0.50839, -2.79832, 0.03612, -0.5527, 0.03113], "time": 0.8667}, {"offset": 6, "vertices": [0.51436, -0.98712, 0.11298, -1.10736, 0.11118, -0.69516, -0.15368, -0.68701, 0.35309, -0.87034, 0.00632, -0.93922, 0.41151, -0.7897, 0.09038, -0.88589, 0.21406, -0.89258, -0.13109, -0.90849, 0.36422, -0.93985, -0.00905, -1.00792, 0.97596, -1.19845, 0.46371, -1.47438, 0.64509, -0.46715, 0.42667, -0.67256, -0.11938, -0.00748, -0.10237, 0.04482, -0.35053, 0.09242, -0.28687, 0.22164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.04096, 0.61174, 2.12005, -0.21268, 0.91755, 0.5033, 1.04018, 0.1154, 2.21377, 0.4886, 1.06902, 0.54265, 0.76255, 0.47053, 0, 0, 0, 0, 0, 0, -3.1483, -0.92704, -3.2653, 0.32982, -4.64692, -2.13528, -5.10944, -0.22307, -3.40964, -1.75053, -3.81821, -0.33413, -1.22186, -0.45828, -1.30452, 0.0368, 0, 0, 0, 0, 0.41907, 0.02936, 0.04555, -0.037, 0.02826, -0.05143, 0, 0, -2.31056, 0.25609, -3.7173, 0.38827, -3.66512, 0.46751, -2.87795, 0.0443, -0.52041, 0.04944], "time": 0.9333}, {"time": 1}, {"offset": 6, "vertices": [1.2859, -2.4678, 0.28245, -2.7684, 0.27796, -1.73789, -0.38421, -1.71753, 0.88273, -2.17584, 0.0158, -2.34806, 1.02877, -1.97424, 0.22596, -2.21474, 0.53516, -2.23145, -0.32773, -2.27121, 0.91054, -2.34962, -0.02262, -2.5198, 2.43991, -2.99612, 1.15929, -3.68596, 1.61273, -1.16786, 1.06667, -1.68141, -0.29844, -0.01869, -0.25593, 0.11206, -0.87634, 0.23105, -0.71718, 0.55409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.10241, 1.52934, 5.30012, -0.53169, 2.29388, 1.25824, 2.60045, 0.2885, 5.53442, 1.22149, 2.67254, 1.35662, 1.90638, 1.17634], "time": 1.1667}, {"time": 1.3333}, {"offset": 6, "vertices": [1.2859, -2.4678, 0.28245, -2.7684, 0.27796, -1.73789, -0.38421, -1.71753, 0.88273, -2.17584, 0.0158, -2.34806, 1.02877, -1.97424, 0.22596, -2.21474, 0.53516, -2.23145, -0.32773, -2.27121, 0.91054, -2.34962, -0.02262, -2.5198, 2.43991, -2.99612, 1.15929, -3.68596, 1.61273, -1.16786, 1.06667, -1.68141, -0.29844, -0.01869, -0.25593, 0.11206, -0.87634, 0.23105, -0.71718, 0.55409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.10241, 1.52934, 5.30012, -0.53169, 2.29388, 1.25824, 2.60045, 0.2885, 5.53442, 1.22149, 2.67254, 1.35662, 1.90638, 1.17634], "time": 1.5}, {"time": 1.6667}]}}}}, "wildmonkey": {"slots": {"batgioiitem6": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem7": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem4": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem5": {"attachment": [{"name": null, "time": 0}]}, "satang4": {"attachment": [{"name": null, "time": 0}]}, "free11": {"attachment": [{"name": null, "time": 0}]}, "free12": {"attachment": [{"name": null, "time": 0}]}, "free13": {"attachment": [{"name": null, "time": 0}]}, "free14": {"attachment": [{"name": null, "time": 0}]}, "free10": {"attachment": [{"name": null, "time": 0}]}, "jackpot10": {"attachment": [{"name": null, "time": 0}]}, "jackpot12": {"attachment": [{"name": null, "time": 0}]}, "jackpot11": {"attachment": [{"name": null, "time": 0}]}, "free15": {"attachment": [{"name": null, "time": 0}]}, "free3": {"attachment": [{"name": null, "time": 0}]}, "wild2": {"attachment": [{"name": null, "time": 0}]}, "free1": {"attachment": [{"name": null, "time": 0}]}, "wild3": {"attachment": [{"name": null, "time": 0}]}, "free6": {"attachment": [{"name": null, "time": 0}]}, "wild0": {"attachment": [{"name": null, "time": 0}]}, "free7": {"attachment": [{"name": null, "time": 0}]}, "wild1": {"attachment": [{"name": null, "time": 0}]}, "free4": {"attachment": [{"name": null, "time": 0}]}, "free5": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem2": {"attachment": [{"name": null, "time": 0}]}, "jackpot14": {"attachment": [{"name": null, "time": 0}]}, "batgioiitem3": {"attachment": [{"name": null, "time": 0}]}, "jackpot13": {"attachment": [{"name": null, "time": 0}]}, "free8": {"attachment": [{"name": null, "time": 0}]}, "free9": {"attachment": [{"name": null, "time": 0}]}, "jackpot1": {"attachment": [{"name": null, "time": 0}]}, "jackpot3": {"attachment": [{"name": null, "time": 0}]}, "jackpot2": {"attachment": [{"name": null, "time": 0}]}, "jackpot5": {"attachment": [{"name": null, "time": 0}]}, "jackpot4": {"attachment": [{"name": null, "time": 0}]}, "jackpot7": {"attachment": [{"name": null, "time": 0}]}, "jackpot6": {"attachment": [{"name": null, "time": 0}]}, "jackpot9": {"attachment": [{"name": null, "time": 0}]}, "jackpot8": {"attachment": [{"name": null, "time": 0}]}, "wild14": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "time": 1.3333}]}, "bonus7": {"attachment": [{"name": null, "time": 0}]}, "bonus1": {"attachment": [{"name": null, "time": 0}]}, "bonus2": {"attachment": [{"name": null, "time": 0}]}, "bonus5": {"attachment": [{"name": null, "time": 0}]}, "satang3": {"attachment": [{"name": null, "time": 0}]}, "bonus6": {"attachment": [{"name": null, "time": 0}]}, "satang2": {"attachment": [{"name": null, "time": 0}]}, "bonus3": {"attachment": [{"name": null, "time": 0}]}, "satang1": {"attachment": [{"name": null, "time": 0}]}, "bonus4": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone49": {"translate": [{"x": 1.54, "y": 0.11, "time": 0}, {"x": -0.2, "y": 0.08, "time": 0.4}, {"x": -2.08, "y": -0.15, "time": 0.8333}, {"x": -0.39, "y": 0.59, "time": 1.2333}, {"x": 1.54, "y": 0.11, "time": 1.6667}]}, "bone50": {"rotate": [{"angle": 0, "time": 0}, {"angle": 8.45, "time": 0.8333}, {"angle": 0, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -0.31, "y": -3.34, "time": 0.8333}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone53": {"rotate": [{"angle": 0, "time": 0}, {"angle": 12.59, "time": 0.1667}, {"angle": 0, "time": 0.3333}, {"angle": 12.59, "time": 0.5}, {"angle": 0, "time": 0.6667}, {"angle": 12.59, "time": 0.8333}, {"angle": 0, "time": 1}, {"angle": 12.59, "time": 1.1667}, {"angle": 0, "time": 1.3333}, {"angle": 12.59, "time": 1.5}, {"angle": 0, "time": 1.6667}]}, "bone54": {"rotate": [{"angle": 0, "time": 0}, {"angle": 10.23, "time": 0.1667}, {"angle": 0, "time": 0.3333}, {"angle": 10.23, "time": 0.5}, {"angle": 0, "time": 0.6667}, {"angle": 10.23, "time": 0.8333}, {"angle": 0, "time": 1}, {"angle": 10.23, "time": 1.1667}, {"angle": 0, "time": 1.3333}, {"angle": 10.23, "time": 1.5}, {"angle": 0, "time": 1.6667}]}, "bone57": {"scale": [{"x": 1, "y": 1, "time": 0.1667}, {"x": 1.363, "y": 1.363, "time": 0.5}, {"x": 1, "y": 1, "time": 0.6}]}, "bone58": {"scale": [{"x": 1, "y": 1, "time": 0.1667}, {"x": 1.363, "y": 1.363, "time": 0.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.6}, {"x": 1, "y": 1, "time": 0.8333}, {"x": 1.121, "y": 1.098, "time": 1.2667}, {"x": 1.102, "y": 1.077, "time": 1.3333}]}, "bone55": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 1.89, "y": 0.22, "time": 0.1667}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 1.89, "y": 0.22, "time": 0.5}, {"x": 0, "y": 0, "time": 0.6667}, {"x": 1.89, "y": 0.22, "time": 0.8333}, {"x": 0, "y": 0, "time": 1}, {"x": 1.89, "y": 0.22, "time": 1.1667}, {"x": 0, "y": 0, "time": 1.3333}, {"x": 1.89, "y": 0.22, "time": 1.5}, {"x": 0, "y": 0, "time": 1.6667}]}, "bone56": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 2.09, "y": 0.08, "time": 0.1667}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 2.09, "y": 0.08, "time": 0.5}, {"x": 0, "y": 0, "time": 0.6667}, {"x": 2.09, "y": 0.08, "time": 0.8333}, {"x": 0, "y": 0, "time": 1}, {"x": 2.09, "y": 0.08, "time": 1.1667}, {"x": 0, "y": 0, "time": 1.3333}, {"x": 2.09, "y": 0.08, "time": 1.5}, {"x": 0, "y": 0, "time": 1.6667}]}}, "deform": {"default": {"wild10": {"wild10": [{"time": 0.6}, {"offset": 86, "curve": "stepped", "vertices": [-1.13428, 0.23894, -3.44388, 1.54179, -4.63121, 1.65994, -3.86837, 0.57549, -2.91382, 0.3867, -1.12841, 0.12108, 0.43917, 0.67166, 1.19027, -0.17707, 1.86192, -0.61624, 1.48754, -1.40281, 0.253, -1.5233, 0, 0, -2.05952, -0.16153, -5.17973, -0.37584, -6.60278, -0.26942, -6.0783, -0.12518, -3.61081, -0.43595, -1.66462, 0.21255, 0, 0, 0.52448, 0.14424, 0.93961, -1.07564, 0.75696, -0.96657, 0.67752, -0.55702, 0.3005, -0.10319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.90551, -0.53565, -1.86724, -0.92693, 0.5186, -0.56292], "time": 0.6667}, {"offset": 86, "vertices": [-1.13428, 0.23894, -3.44388, 1.54179, -4.63121, 1.65994, -3.86837, 0.57549, -2.91382, 0.3867, -1.12841, 0.12108, 0.43917, 0.67166, 1.19027, -0.17707, 1.86192, -0.61624, 1.48754, -1.40281, 0.253, -1.5233, 0, 0, -2.05952, -0.16153, -5.17973, -0.37584, -6.60278, -0.26942, -6.0783, -0.12518, -3.61081, -0.43595, -1.66462, 0.21255, 0, 0, 0.52448, 0.14424, 0.93961, -1.07564, 0.75696, -0.96657, 0.67752, -0.55702, 0.3005, -0.10319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.90551, -0.53565, -1.86724, -0.92693, 0.5186, -0.56292], "time": 0.7}, {"time": 0.7667}]}}}}}}